/**
 * API route for fetching operatories
 */

import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const date = searchParams.get('date');

  if (!date) {
    return NextResponse.json(
      { error: 'Date parameter is required' },
      { status: 400 }
    );
  }

  // Log the date from the URL and ensure it's in YYYY-MM-DD format
  console.log(`API operatories: URL date parameter is ${date}`);
  
  // Parse and validate the date
  let dateToUse: string;
  try {
    // Ensure the date is in YYYY-MM-DD format
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      throw new Error('Invalid date format');
    }
    dateToUse = dateObj.toISOString().split('T')[0];
    console.log(`API operatories: Using normalized date ${dateToUse}`);
  } catch (err) {
    console.error('Invalid date parameter:', err);
    return NextResponse.json(
      { error: 'Invalid date format. Please use YYYY-MM-DD format' },
      { status: 400 }
    );
  }

  try {
    const credentials = loadCredentials();
    const client = new SikkaApiClient(credentials);

    console.log(`API: Fetching operatories with appointments for date: ${dateToUse}`);
    
    // Authenticate and get operatories
    await client.authenticate();
    const operatories = await client.getOperatories(dateToUse);
    
    console.log(`API: Found ${operatories.length} operatories with appointments`);
    
    if (operatories.length === 0) {
      console.warn('No operatories with appointments found for the requested date');
    }

    return NextResponse.json(operatories);
  } catch (error) {
    console.error('Error fetching operatories:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch operatories' },
      { status: 500 }
    );
  }
}
