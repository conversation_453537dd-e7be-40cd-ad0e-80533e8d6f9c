-- Voice Notes Database Schema
-- This should be created at: \\SERVER\\Recordings\voice_notes.db

-- Table to track voice recordings and their workflow status
CREATE TABLE IF NOT EXISTS voice_recordings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- File information
    filename TEXT NOT NULL,
    device_id TEXT NOT NULL,
    file_path TEXT NOT NULL,           -- Full server path to audio file
    file_size INTEGER,
    duration_seconds REAL,
    
    -- Import information
    imported_at DATETIME NOT NULL,
    json_receipt_path TEXT,
    
    -- Assignment and workflow status
    status TEXT DEFAULT 'unassigned' CHECK (status IN ('unassigned', 'assigned', 'transcribing', 'transcribed', 'reviewing', 'completed')),
    assigned_patient_id TEXT,
    assigned_appointment_id TEXT,
    assigned_appointment_date DATE,
    assigned_provider TEXT,
    assigned_at DATETIME,
    assigned_by TEXT,
    
    -- Transcription and processing
    transcription TEXT,
    transcription_confidence REAL,
    transcribed_at DATETIME,
    
    -- Clinical notes
    clinical_summary TEXT,
    clinical_notes TEXT,
    notes_posted_at DATETIME,
    posted_to_dentrix BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    category TEXT DEFAULT 'clinical' CHECK (category IN ('clinical', 'administrative', 'consultation', 'other')),
    tags TEXT,                         -- JSON array of tags
    notes TEXT,                        -- User notes/comments
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for fast queries
    UNIQUE(file_path)
);

-- Table to track appointment assignments
CREATE TABLE IF NOT EXISTS appointment_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recording_id INTEGER NOT NULL,
    appointment_id TEXT NOT NULL,
    patient_id TEXT,
    appointment_date DATE,
    provider TEXT,
    operatory TEXT,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_by TEXT,
    
    FOREIGN KEY (recording_id) REFERENCES voice_recordings(id) ON DELETE CASCADE,
    UNIQUE(recording_id, appointment_id)
);

-- Table to track transcription jobs and results
CREATE TABLE IF NOT EXISTS transcription_jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recording_id INTEGER NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    engine TEXT DEFAULT 'whisper',     -- whisper, azure, google, etc.
    started_at DATETIME,
    completed_at DATETIME,
    confidence_score REAL,
    error_message TEXT,
    raw_transcription TEXT,
    cleaned_transcription TEXT,
    
    FOREIGN KEY (recording_id) REFERENCES voice_recordings(id) ON DELETE CASCADE
);

-- Table to track clinical note generation
CREATE TABLE IF NOT EXISTS clinical_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recording_id INTEGER NOT NULL,
    appointment_id TEXT,
    
    -- Generated content
    summary TEXT,
    soap_notes TEXT,                   -- SOAP format notes
    procedure_codes TEXT,              -- JSON array of suggested codes
    
    -- AI processing info
    ai_model TEXT,
    processing_timestamp DATETIME,
    confidence_score REAL,
    
    -- Review and approval
    reviewed_by TEXT,
    reviewed_at DATETIME,
    approved BOOLEAN DEFAULT FALSE,
    
    -- Dentrix integration
    posted_to_dentrix BOOLEAN DEFAULT FALSE,
    dentrix_note_id TEXT,
    posted_at DATETIME,
    posted_by TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (recording_id) REFERENCES voice_recordings(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_voice_recordings_status ON voice_recordings(status);
CREATE INDEX IF NOT EXISTS idx_voice_recordings_date ON voice_recordings(assigned_appointment_date);
CREATE INDEX IF NOT EXISTS idx_voice_recordings_device ON voice_recordings(device_id);
CREATE INDEX IF NOT EXISTS idx_voice_recordings_imported ON voice_recordings(imported_at);
CREATE INDEX IF NOT EXISTS idx_appointment_assignments_date ON appointment_assignments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_clinical_notes_appointment ON clinical_notes(appointment_id);

-- Trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_voice_recordings_updated_at 
    AFTER UPDATE ON voice_recordings
BEGIN
    UPDATE voice_recordings 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_clinical_notes_updated_at 
    AFTER UPDATE ON clinical_notes
BEGIN
    UPDATE clinical_notes 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;
