import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    console.log(`API: Searching for patients with query: ${query}`);

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();
    const patients = await sikkaClient.searchPatients(query);

    console.log(`API: Found ${patients.length} patients`);

    return NextResponse.json({
      patients,
      total: patients.length
    });

  } catch (error) {
    console.error('Patient search API error:', error);
    return NextResponse.json(
      { error: 'Failed to search patients' },
      { status: 500 }
    );
  }
}
