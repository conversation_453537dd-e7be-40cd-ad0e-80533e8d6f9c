"use client";

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, AlertTriangle } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isLoading?: boolean;
}

interface ChatInterfaceProps {
  isDarkMode: boolean;
}

export function ChatInterface({ isDarkMode }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your AI Office Manager. I can help you analyze practice data while keeping all patient information secure and private. What would you like to know about your practice?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Add loading message
      const loadingMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: '',
        timestamp: new Date(),
        isLoading: true
      };
      setMessages(prev => [...prev, loadingMessage]);

      // Call AI processing endpoint
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          history: messages.slice(-10) // Send last 10 messages for context
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const data = await response.json();

      // Remove loading message and add actual response
      setMessages(prev => {
        const withoutLoading = prev.filter(msg => !msg.isLoading);
        return [...withoutLoading, {
          id: (Date.now() + 2).toString(),
          type: 'assistant',
          content: data.response,
          timestamp: new Date()
        }];
      });

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Remove loading message and add error message
      setMessages(prev => {
        const withoutLoading = prev.filter(msg => !msg.isLoading);
        return [...withoutLoading, {
          id: (Date.now() + 2).toString(),
          type: 'assistant',
          content: 'I apologize, but I encountered an error processing your request. Please try again.',
          timestamp: new Date()
        }];
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-120px)] w-full">      {/* Messages Container - Command Line Style */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 font-mono text-sm leading-relaxed min-h-0"
        style={
          isDarkMode
            ? { backgroundColor: '#0f172a', color: '#e2e8f0' }
            : { backgroundColor: '#fafafa', color: '#1e293b' }
        }
      >
        {messages.map((message) => (
          <div key={message.id} className="mb-4">
            {message.type === 'user' ? (
              <div className="flex items-start space-x-2">
                <span
                  className="text-blue-500 font-bold"
                  style={{ color: isDarkMode ? '#60a5fa' : '#2563eb' }}
                >
                  &gt;
                </span>
                <div className="flex-1 whitespace-pre-wrap">{message.content}</div>
              </div>
            ) : (
              <div className="mt-2">
                {message.isLoading ? (
                  <div className="flex items-center space-x-2">
                    <span
                      className="text-green-500 font-bold"
                      style={{ color: isDarkMode ? '#34d399' : '#059669' }}
                    >
                      AI:
                    </span>
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-start space-x-2">
                    <span
                      className="text-green-500 font-bold"
                      style={{ color: isDarkMode ? '#34d399' : '#059669' }}
                    >
                      AI:
                    </span>
                    <div className="flex-1 whitespace-pre-wrap">{message.content}</div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Command Line Input */}
      <div
        className="border-t p-4 font-mono"
        style={
          isDarkMode
            ? { backgroundColor: '#1e293b', borderColor: '#374151' }
            : { backgroundColor: '#f8fafc', borderColor: '#e2e8f0' }
        }
      >
        <div className="flex items-center space-x-2">
          <span
            className="text-blue-500 font-bold"
            style={{ color: isDarkMode ? '#60a5fa' : '#2563eb' }}
          >
            &gt;
          </span>
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about your practice data..."
            disabled={isLoading}
            className="flex-1 bg-transparent border-none outline-none font-mono text-sm"
            style={{ color: isDarkMode ? '#e2e8f0' : '#1e293b' }}
          />
          {inputValue.trim() && (
            <button
              onClick={handleSendMessage}
              disabled={isLoading}
              className="text-blue-500 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ color: isDarkMode ? '#60a5fa' : '#2563eb' }}
            >
              <Send className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Example Queries - Only show when no messages */}
      {messages.length <= 1 && (
        <div
          className="p-4 border-t font-mono text-xs"
          style={
            isDarkMode
              ? { backgroundColor: '#1e293b', borderColor: '#374151', color: '#64748b' }
              : { backgroundColor: '#f8fafc', borderColor: '#e2e8f0', color: '#64748b' }
          }
        >
          <div className="text-center mb-3">Try asking:</div>
          <div className="flex flex-wrap gap-2 justify-center">
            {[
              "How many cleanings did we do last month?",
              "Show me our most common procedures",
              "Which patients are overdue for checkups?",
              "What's our appointment volume this quarter?"
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setInputValue(example)}
                className="text-xs px-2 py-1 border rounded hover:opacity-80 transition-opacity"
                style={
                  isDarkMode
                    ? { borderColor: '#374151', color: '#9ca3af' }
                    : { borderColor: '#d1d5db', color: '#6b7280' }
                }
              >
                {example}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Subtle Privacy Disclaimer at Bottom */}
      <div
        className="px-4 py-2 text-xs text-center font-mono"
        style={{ color: isDarkMode ? '#4b5563' : '#9ca3af' }}
      >
        <AlertTriangle className="h-3 w-3 inline mr-1" />
        Privacy Protected: Patient data anonymized before AI processing
      </div>
    </div>
  );
}
