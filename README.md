# Dental Schedule App

A Next.js application for managing dental schedules and operatories.

## Current Status
- Application is set up with Next.js 15.3.2
- Integrated with Sikka API for fetching operatories and appointments
- Basic navigation between home, operatories, and schedule views
- Dark/light theme support
- Development server runs on port 3001

## Prerequisites
- Node.js (v16 or later recommended)
- npm (comes with Node.js)
- Git

## Getting Started

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd augmentapp
   ```

2. **Install dependencies**
   ```bash
   cd dental-schedule-next
   npm install
   ```

3. **Run the development server**
   - Option 1: Using VS Code
     - Open the project in VS Code
     - Press `F5` to start the development server
     - The app will open automatically in your default browser at `http://localhost:3001`
   
   - Option 2: Using command line
     ```bash
     npm run dev
     ```
     Then open [http://localhost:3001](http://localhost:3001) in your browser

## Project Structure

- `/dental-schedule-next` - Main Next.js application
  - `/src/app` - Application pages and components
  - `/src/lib` - Utility functions and API clients
  - `/public` - Static files

## Development Workflow

- `app.bat` - Convenience script to start the development server
- `.vscode/launch.json` - VS Code debug configuration

## Environment Variables

Create a `.env.local` file in the `dental-schedule-next` directory with the following variables:

```
# API Configuration
NEXT_PUBLIC_API_BASE_URL=your_api_url_here
NEXT_PUBLIC_API_KEY=your_api_key_here
```

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Commit and push to your branch
4. Create a pull request

## License

This project is licensed under the MIT License.
