"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { PageHeader } from "@/components/ui/page-header";
import { EnhancedCalendar } from "@/components/ui/enhanced-calendar";
import { format } from "date-fns";
import { Search, User } from "lucide-react";
import { ChatInterface } from "@/components/ai/chat-interface";
import { VoiceNotesInterface } from "@/components/voice/voice-notes-interface";

interface Patient {
  id: string;
  name: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  middleInitial?: string;
  preferredName?: string;
  dateOfBirth?: string;
  age?: string;
  gender?: string;
  genderInitial?: string;
  phone?: string;
  email?: string;
  lastVisit?: string;
  firstVisit?: string;
  recentVisitCount?: number;
}

export default function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchInputRef = useRef<HTMLInputElement>(null);

  const today = new Date();
  today.setHours(12, 0, 0, 0);

  const [selectedDate, setSelectedDate] = useState(today);  const tabFromUrl = searchParams.get('tab') as 'schedule' | 'patient' | 'ai' | 'voice' | null;
  const [activeTab, setActiveTab] = useState<'schedule' | 'patient' | 'ai' | 'voice'>(tabFromUrl || 'schedule');
  
  useEffect(() => {
    const tab = searchParams.get('tab') as 'schedule' | 'patient' | 'ai' | 'voice' | null;
    if (tab && tab !== activeTab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleTabChange = (tab: 'schedule' | 'patient' | 'ai' | 'voice') => {
    setActiveTab(tab); // Update state immediately
    const params = new URLSearchParams(searchParams.toString());
    if (tab === 'schedule') {
      params.delete('tab');
    } else {
      params.set('tab', tab);
    }
    const newUrl = params.toString() ? `/?${params.toString()}` : '/';
    router.push(newUrl, { scroll: false });
  };
  const searchFromUrl = searchParams.get('search') || '';
  const [searchTerm, setSearchTerm] = useState(searchFromUrl);
  const [searchResults, setSearchResults] = useState<Patient[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };
    checkDarkMode();
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
    return () => observer.disconnect();
  }, []);

  const handleDateChange = (date: Date) => {
    const normalizedDate = new Date(date);
    normalizedDate.setHours(12, 0, 0, 0);
    setSelectedDate(normalizedDate);
  };

  const formattedDate = format(selectedDate, "yyyy-MM-dd");

  const viewDrLowellSchedule = () => {
    router.push(`/schedule?date=${formattedDate}&operatories[]=${encodeURIComponent("DL01")}&operatories[]=${encodeURIComponent("DL02")}`);
  };

  const selectOtherOperatories = () => {
    router.push(`/operatories?date=${formattedDate}`);
  };

  // useEffect(() => {
  //   const tabFromUrl = searchParams.get('tab') as 'schedule' | 'patient' | 'ai' | 'voice' | null;
  //   const newTab = tabFromUrl || 'schedule';
  //   if (newTab !== activeTab) {
  //     setActiveTab(newTab);
  //     if (newTab === 'patient') {
  //       setError(null);
  //       setSearchResults([]);
  //     }
  //   }
  // }, [searchParams, activeTab]);

  // useEffect(() => {
  //   const searchFromUrl = searchParams.get('search') || '';
  //   if (searchFromUrl !== searchTerm) {
  //     setSearchTerm(searchFromUrl);
  //   }
  //   if (searchFromUrl && activeTab === 'patient' && searchFromUrl.trim()) {
  //     handlePatientSearchWithQuery(searchFromUrl);
  //   } else if (!searchFromUrl && activeTab === 'patient') {
  //     setSearchResults([]);
  //     setError(null);
  //   }
  // }, [searchParams, activeTab, searchTerm]);

  // useEffect(() => {
  //   if (activeTab === 'patient' && searchInputRef.current) {
  //     searchInputRef.current.focus();
  //   }
  // }, [activeTab]);
  const handlePatientSearch = async () => {
    if (!searchTerm.trim()) {
      setError('Please enter a search term');
      return;
    }
    const params = new URLSearchParams();
    params.set('tab', 'patient');
    params.set('search', searchTerm.trim());
    router.push(`/?${params.toString()}`);
  };

  const handlePatientSearchWithQuery = async (query: string) => {
    setIsSearching(true);
    setError(null);
    try {
      const response = await fetch(`/api/patients/search?q=${encodeURIComponent(query)}`);
      if (!response.ok) {
        throw new Error('Failed to search patients');
      }
      const data = await response.json();
      setSearchResults(data.patients || []);
      if (data.patients?.length === 0) {
        setError('No patients found matching your search');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('Failed to search patients. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handlePatientSelect = (patient: Patient) => {
    const params = new URLSearchParams();
    params.set('q', searchTerm);
    params.set('patient', patient.id);
    router.push(`/patient-search?${params.toString()}`);
  };

  const formatPatientName = (patient: Patient) => {
    const parts = [patient.firstName, patient.middleName, patient.lastName].filter(Boolean);
    return parts.join(' ');
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <PageHeader 
        title="Dental Practice Manager" 
        isHomePage={true}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
            Welcome to Dental Practice Manager
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
            View daily schedules and search patient clinical notes
          </p>

          {/* Navigation Tabs - Reordered and Renamed */}
          <div className="mb-8">            <div
              className="flex justify-center space-x-1 rounded-lg p-1 border shadow-md"
              style={
                isDarkMode
                  ? { backgroundColor: '#374151', borderColor: '#4b5563' }
                  : { backgroundColor: '#e5e7eb', borderColor: '#d1d5db' }
              }
            >
              {/* 1. Schedule Viewer */}
              <button
                onClick={() => handleTabChange('schedule')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'schedule'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                Schedule Viewer
              </button>
              {/* 2. Voice Notes */}
              <button
                onClick={() => handleTabChange('voice')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'voice'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                Voice Notes
              </button>
              {/* 3. Patient Search */}
              <button
                onClick={() => handleTabChange('patient')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'patient'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                Patient Search
              </button>
              {/* 4. AI Office Manager (was AI Chat) */}
              <button
                onClick={() => handleTabChange('ai')} 
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'ai'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                AI Office Manager 
              </button>
            </div>
          </div>

          {/* Conditional Content Based on Active Tab */}
          {activeTab === 'schedule' ? (
            <>
              <div className="mb-6">
                <EnhancedCalendar initialDate={selectedDate} onDateChange={handleDateChange} />
              </div>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={viewDrLowellSchedule}
                  className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-lg font-medium"
                >
                  View Dr. Lowell's Schedule
                </button>
                <button
                  onClick={selectOtherOperatories}
                  className="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-lg font-medium"
                >
                  Select Other Operatories
                </button>
              </div>
            </>
          ) : activeTab === 'patient' ? (
            /* Patient Search Interface */
            <div className="max-w-2xl mx-auto">
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
                  Search Patients
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
                  Search by first name, last name, or partial matches
                </p>
                <div className="flex space-x-2">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)} // Corrected: setSearchTerm instead of handleSearchChange
                      onKeyPress={(e) => e.key === 'Enter' && handlePatientSearch()}
                      placeholder="Enter patient name..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button
                    onClick={handlePatientSearch}
                    disabled={isSearching}
                    className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSearching ? 'Searching...' : 'Search'}
                  </button>
                </div>
              </div>
              {error && (
                <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  <p className="text-red-800 dark:text-red-200">{error}</p>
                </div>
              )}
              {searchResults.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Search Results ({searchResults.length})
                  </h4>
                  <div className="space-y-2">
                    {searchResults.map((patient) => (
                      <button
                        key={patient.id}
                        onClick={() => handlePatientSelect(patient)}
                        className="w-full p-4 text-left bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md border border-gray-300 dark:border-gray-700 transition-colors shadow-sm"
                      >
                        <div className="flex items-center space-x-3">
                          <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          <div>
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-gray-900 dark:text-white">
                                {formatPatientName(patient)}
                              </p>
                              {patient.age && patient.genderInitial && (
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                  ({patient.age}{patient.genderInitial})
                                </span>
                              )}
                            </div>
                            {patient.lastVisit && (
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                Last visit: {patient.lastVisit}
                              </p>
                            )}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : activeTab === 'ai' ? (
            /* AI Office Manager Interface */
            <div className="w-full h-full">
              <ChatInterface isDarkMode={isDarkMode} />
            </div>
          ) : (
            /* Voice Notes Interface */
            <div className="w-full h-full">
              <VoiceNotesInterface isDarkMode={isDarkMode} />
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
