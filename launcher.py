#!/usr/bin/env python3
"""
Terminal-based Dental Application Launcher
==========================================

A comprehensive terminal launcher that provides:
- Numbered menu options for easy selection
- Process management and monitoring
- Automatic web app startup with all dependencies
- Audio transfer GUI launching
- Real-time process status display

Usage:
    python launcher.py
"""

import subprocess
import webbrowser
import sys
import os
import time
import threading
import psutil
import json
from pathlib import Path
from datetime import datetime
import signal

# Configuration
BASE_DIR = Path(__file__).parent
AUDIO_SCRIPT = BASE_DIR / "archive" / "audio_transfer_gui.py"
NEXT_APP_DIR = BASE_DIR / "dental-schedule-next"
CONDUCTOR_SCRIPT = BASE_DIR / "conductor.py"
PROCESS_FILE = BASE_DIR / "launcher_processes.json"
DEFAULT_PORT = 3001

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

class ProcessManager:
    def __init__(self):
        self.processes = {}
        self.load_processes()

    def load_processes(self):
        """Load saved process information."""
        if PROCESS_FILE.exists():
            try:
                with open(PROCESS_FILE, 'r') as f:
                    data = json.load(f)
                    # Verify processes are still running
                    for name, pid in data.items():
                        if self.is_process_running(pid):
                            self.processes[name] = pid
                        else:
                            print(f"{Colors.WARNING}⚠ Process {name} (PID {pid}) is no longer running{Colors.ENDC}")
            except Exception as e:
                print(f"{Colors.WARNING}⚠ Could not load process file: {e}{Colors.ENDC}")

    def save_processes(self):
        """Save current process information."""
        try:
            with open(PROCESS_FILE, 'w') as f:
                json.dump(self.processes, f, indent=2)
        except Exception as e:
            print(f"{Colors.WARNING}⚠ Could not save process file: {e}{Colors.ENDC}")

    def is_process_running(self, pid):
        """Check if a process is still running."""
        try:
            return psutil.pid_exists(pid)
        except:
            return False

    def add_process(self, name, process):
        """Add a process to tracking."""
        self.processes[name] = process.pid
        self.save_processes()
        print(f"{Colors.OKGREEN}✓ Started {name} (PID: {process.pid}){Colors.ENDC}")

    def remove_process(self, name):
        """Remove a process from tracking."""
        if name in self.processes:
            del self.processes[name]
            self.save_processes()

    def kill_process(self, name):
        """Kill a tracked process."""
        if name not in self.processes:
            print(f"{Colors.FAIL}✗ Process {name} not found{Colors.ENDC}")
            return False

        pid = self.processes[name]
        try:
            if self.is_process_running(pid):
                process = psutil.Process(pid)
                process.terminate()
                time.sleep(2)
                if process.is_running():
                    process.kill()
                print(f"{Colors.OKGREEN}✓ Killed {name} (PID: {pid}){Colors.ENDC}")
            else:
                print(f"{Colors.WARNING}⚠ Process {name} (PID: {pid}) was already stopped{Colors.ENDC}")
            self.remove_process(name)
            return True
        except Exception as e:
            print(f"{Colors.FAIL}✗ Failed to kill {name}: {e}{Colors.ENDC}")
            return False

    def get_status(self):
        """Get status of all tracked processes."""
        status = {}
        for name, pid in self.processes.items():
            if self.is_process_running(pid):
                try:
                    process = psutil.Process(pid)
                    status[name] = {
                        'pid': pid,
                        'status': process.status(),
                        'cpu_percent': process.cpu_percent(),
                        'memory_mb': round(process.memory_info().rss / 1024 / 1024, 1),
                        'running': True
                    }
                except:
                    status[name] = {'pid': pid, 'running': False}
            else:
                status[name] = {'pid': pid, 'running': False}
                self.remove_process(name)
        return status

# Global process manager
pm = ProcessManager()

def print_header(message):
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{message.center(60)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}\n")

def print_success(message):
    print(f"{Colors.OKGREEN}✓ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.OKBLUE}ℹ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def launch_audio_gui():
    """Launch the audio transfer GUI in a new window."""
    if not AUDIO_SCRIPT.exists():
        print_error(f"Audio script not found: {AUDIO_SCRIPT}")
        return False

    try:
        # Launch in new command window
        cmd = f'start "Audio Transfer GUI" cmd /k "{sys.executable} \\"{AUDIO_SCRIPT}\\""'
        process = subprocess.Popen(cmd, shell=True)
        pm.add_process("audio_gui", process)
        return True
    except Exception as e:
        print_error(f"Failed to launch audio GUI: {e}")
        return False

def launch_next_server():
    """Start the Next.js development server in a new window."""
    if not NEXT_APP_DIR.exists():
        print_error(f"Next.js directory not found: {NEXT_APP_DIR}")
        return False

    try:
        # Launch in new command window
        cmd = f'start "Next.js Server" cmd /k "cd /d \\"{NEXT_APP_DIR}\\" && npm run dev"'
        process = subprocess.Popen(cmd, shell=True)
        pm.add_process("next_server", process)
        return True
    except Exception as e:
        print_error(f"Failed to launch Next.js server: {e}")
        return False

def launch_conductor():
    """Launch the conductor script for full application startup."""
    if not CONDUCTOR_SCRIPT.exists():
        print_error(f"Conductor script not found: {CONDUCTOR_SCRIPT}")
        return False

    try:
        # Launch in new command window
        cmd = f'start "Dental App Conductor" cmd /k "{sys.executable} \\"{CONDUCTOR_SCRIPT}\\""'
        process = subprocess.Popen(cmd, shell=True)
        pm.add_process("conductor", process)
        return True
    except Exception as e:
        print_error(f"Failed to launch conductor: {e}")
        return False

def open_browser():
    """Open the schedule app in browser."""
    try:
        webbrowser.open(f"http://localhost:{DEFAULT_PORT}")
        print_success(f"Opened browser to http://localhost:{DEFAULT_PORT}")
        return True
    except Exception as e:
        print_error(f"Failed to open browser: {e}")
        return False

def auto_start_web_app():
    """Automatically start everything needed for the web app."""
    print_header("Auto-Starting Dental Web Application")

    print_info("Step 1: Starting conductor (handles database, dependencies, audio transfer)...")
    if launch_conductor():
        print_info("Conductor started - it will handle setup and start the web server")

        print_info("Step 2: Waiting for server to start...")
        time.sleep(8)  # Give server time to start

        print_info("Step 3: Opening browser...")
        open_browser()

        print_success("Web application startup complete!")
        print_info("The conductor window will show detailed startup progress")
        return True
    else:
        print_error("Failed to start conductor")
        return False

def show_process_status():
    """Display current process status."""
    print_header("Running Processes")

    status = pm.get_status()
    if not status:
        print_info("No tracked processes running")
        return

    print(f"{'Process':<15} {'PID':<8} {'Status':<12} {'CPU%':<8} {'Memory(MB)':<12} {'Running'}")
    print("-" * 70)

    for name, info in status.items():
        if info['running']:
            cpu = f"{info.get('cpu_percent', 0):.1f}%" if 'cpu_percent' in info else "N/A"
            memory = f"{info.get('memory_mb', 0):.1f}" if 'memory_mb' in info else "N/A"
            status_text = info.get('status', 'unknown')
            running_icon = f"{Colors.OKGREEN}✓{Colors.ENDC}"
        else:
            cpu = memory = status_text = "N/A"
            running_icon = f"{Colors.FAIL}✗{Colors.ENDC}"

        print(f"{name:<15} {info['pid']:<8} {status_text:<12} {cpu:<8} {memory:<12} {running_icon}")

def kill_process_menu():
    """Interactive menu to kill processes."""
    status = pm.get_status()
    running_processes = {name: info for name, info in status.items() if info['running']}

    if not running_processes:
        print_info("No running processes to kill")
        return

    print_header("Kill Process")
    print("Select a process to kill:")

    process_list = list(running_processes.keys())
    for i, name in enumerate(process_list, 1):
        info = running_processes[name]
        print(f"{i}. {name} (PID: {info['pid']})")

    print("0. Cancel")

    try:
        choice = int(input(f"\n{Colors.OKCYAN}Enter choice (0-{len(process_list)}): {Colors.ENDC}"))
        if choice == 0:
            return
        elif 1 <= choice <= len(process_list):
            process_name = process_list[choice - 1]
            if pm.kill_process(process_name):
                print_success(f"Process {process_name} killed successfully")
            else:
                print_error(f"Failed to kill process {process_name}")
        else:
            print_error("Invalid choice")
    except ValueError:
        print_error("Invalid input")

def show_menu():
    """Display the main menu."""
    print_header("Dental Application Launcher")
    print(f"{Colors.OKBLUE}Choose an option:{Colors.ENDC}")
    print()
    print("🚀 Quick Start:")
    print("  1. Auto-start Web Application (Recommended)")
    print("     └─ Runs conductor, sets up database, starts server, opens browser")
    print()
    print("📱 Individual Applications:")
    print("  2. Launch Audio Transfer GUI")
    print("  3. Start Next.js Server Only")
    print("  4. Run Conductor (Full Setup)")
    print("  5. Open Browser to Web App")
    print()
    print("🔧 Process Management:")
    print("  6. Show Running Processes")
    print("  7. Kill a Process")
    print("  8. Kill All Tracked Processes")
    print()
    print("ℹ️  Other:")
    print("  9. Refresh Process Status")
    print("  0. Exit")
    print()

def kill_all_processes():
    """Kill all tracked processes."""
    status = pm.get_status()
    running_processes = {name: info for name, info in status.items() if info['running']}

    if not running_processes:
        print_info("No running processes to kill")
        return

    print_warning(f"This will kill {len(running_processes)} running processes:")
    for name, info in running_processes.items():
        print(f"  - {name} (PID: {info['pid']})")

    confirm = input(f"\n{Colors.WARNING}Are you sure? (y/N): {Colors.ENDC}").lower()
    if confirm == 'y':
        killed_count = 0
        for name in running_processes.keys():
            if pm.kill_process(name):
                killed_count += 1
        print_success(f"Killed {killed_count} processes")
    else:
        print_info("Cancelled")

def handle_menu_choice(choice):
    """Handle user menu selection."""
    if choice == "1":
        auto_start_web_app()
    elif choice == "2":
        launch_audio_gui()
    elif choice == "3":
        launch_next_server()
    elif choice == "4":
        launch_conductor()
    elif choice == "5":
        open_browser()
    elif choice == "6":
        show_process_status()
    elif choice == "7":
        kill_process_menu()
    elif choice == "8":
        kill_all_processes()
    elif choice == "9":
        print_info("Refreshing process status...")
        pm.load_processes()
        show_process_status()
    elif choice == "0":
        return False
    else:
        print_error("Invalid choice. Please try again.")

    return True

def cleanup_on_exit():
    """Cleanup function called on exit."""
    print_info("Launcher exiting...")
    # Note: We don't kill processes on exit - they should continue running

def main():
    """Main program loop."""
    print_header("Dental Application Launcher Starting")
    print_info(f"Base directory: {BASE_DIR}")
    print_info(f"Process tracking file: {PROCESS_FILE}")

    # Register cleanup function
    def signal_handler(sig, frame):
        print("\n")
        cleanup_on_exit()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    try:
        while True:
            show_menu()

            try:
                choice = input(f"{Colors.OKCYAN}Enter your choice (0-9): {Colors.ENDC}").strip()
                print()  # Add blank line after input

                if not handle_menu_choice(choice):
                    break

                # Pause before showing menu again (except for exit)
                if choice != "0":
                    input(f"\n{Colors.OKBLUE}Press Enter to continue...{Colors.ENDC}")
                    clear_screen()

            except KeyboardInterrupt:
                print("\n")
                break
            except EOFError:
                print("\n")
                break

    except Exception as e:
        print_error(f"Unexpected error: {e}")
    finally:
        cleanup_on_exit()

if __name__ == "__main__":
    main()