import tkinter as tk
from tkinter import messagebox
import subprocess
import webbrowser
import sys
import os
import threading
import time

# Paths
BASE_DIR = os.path.dirname(__file__)
AUDIO_SCRIPT = os.path.join(BASE_DIR, "archive", "audio_transfer_gui.py")
NEXT_APP_DIR = os.path.join(BASE_DIR, "dental-schedule-next")

def launch_audio():
    """Launch the audio transfer GUI in new window."""
    if os.path.isfile(AUDIO_SCRIPT):
        cmd = f'start "" cmd /k "{sys.executable} \\"{AUDIO_SCRIPT}\\""'
        subprocess.Popen(cmd, shell=True)
    else:
        messagebox.showerror("Error", f"Audio script not found:\n{AUDIO_SCRIPT}")

def launch_server():
    """Start the Next.js development server in new window."""
    if os.path.isdir(NEXT_APP_DIR):
        cmd = f'start "" cmd /k "cd /d \\"{NEXT_APP_DIR}\\" && npm run dev"'
        subprocess.Popen(cmd, shell=True)
    else:
        messagebox.showerror("Error", f"Next.js directory not found:\n{NEXT_APP_DIR}")

def launch_server_and_open():
    """Start server, wait briefly, then open browser."""
    launch_server()
    # wait for server to bind
    time.sleep(5)
    webbrowser.open("http://localhost:3001")

def open_browser():
    """Open the schedule app in browser."""
    webbrowser.open("http://localhost:3001")

def main():
    root = tk.Tk()
    root.title("Dental App Launcher")
    root.geometry("300x200")
    frame = tk.Frame(root, padx=10, pady=10)
    frame.pack(expand=True, fill="both")

    btn_audio = tk.Button(frame, text="Launch Audio App", width=30, command=launch_audio)
    btn_audio.pack(pady=5)

    btn_server = tk.Button(frame, text="Start Web Server", width=30, command=launch_server)
    btn_server.pack(pady=5)

    btn_open = tk.Button(frame, text="Open Schedule in Browser", width=30, command=open_browser)
    btn_open.pack(pady=5)

    btn_all = tk.Button(frame, text="Start & Open Schedule", width=30,
                        command=lambda: threading.Thread(target=launch_server_and_open).start())
    btn_all.pack(pady=5)

    root.mainloop()

if __name__ == "__main__":
    main()