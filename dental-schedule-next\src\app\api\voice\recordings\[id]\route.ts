import { NextRequest, NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const recordingId = params.id;
    
    if (!recordingId) {
      return NextResponse.json({ error: 'Recording ID required' }, { status: 400 });
    }
    
    // Extract filename from ID (format: 'net-filename' or 'local-filename')
    const [source, ...filenameParts] = recordingId.split('-');
    const filename = filenameParts.join('-');
    
    let deleted = false;
    
    if (source === 'net') {
      // Try to delete from network share
      const networkPath = '\\\\192.168.0.2\\share\\recordings';
      const networkFilePath = path.join(networkPath, filename);
      
      try {
        if (existsSync(networkFilePath)) {
          await unlink(networkFilePath);
          deleted = true;
        }
      } catch (error) {
        console.error('Failed to delete from network share:', error);
      }
    }
    
    if (source === 'local' || !deleted) {
      // Try to delete from local backup
      const localBackupPath = path.join(process.cwd(), 'voice-recordings');
      const localFilePath = path.join(localBackupPath, filename);
      
      try {
        if (existsSync(localFilePath)) {
          await unlink(localFilePath);
          deleted = true;
        }
      } catch (error) {
        console.error('Failed to delete from local storage:', error);
      }
    }
    
    if (!deleted) {
      return NextResponse.json({ 
        error: 'Recording not found or could not be deleted' 
      }, { status: 404 });
    }
    
    return NextResponse.json({ 
      success: true,
      message: 'Recording deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete error:', error);
    return NextResponse.json({ 
      error: 'Delete failed' 
    }, { status: 500 });
  }
}
