"use client";

import { useState, useEffect, useC<PERSON>back, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { format } from "date-fns";
import useS<PERSON> from "swr";
import { PageHeader } from "@/components/ui/page-header";

// Helper function to calculate appointment height based on length
function calculateAppointmentHeight(lengthInMinutes: number) {
  // Base height for a 10-minute appointment
  const baseHeight = 30; // pixels

  // Calculate height proportionally, with a minimum height
  // Increase the minimum height to ensure all content is visible
  const calculatedHeight = Math.max(
    baseHeight * (lengthInMinutes / 10),
    120 // Increased minimum height in pixels to prevent content cutoff
  );

  console.log(`Calculated height for ${lengthInMinutes} minutes: ${calculatedHeight}px`);
  return calculatedHeight;
}

// Helper function to convert time string to minutes since midnight
function timeToMinutes(timeStr: string) {
  if (!timeStr) return 0;

  try {
    // Log the time string for debugging
    console.log(`Parsing time string: "${timeStr}"`);

    // Try to parse the time string with various formats
    let hours = 0;
    let minutes = 0;

    // Format: "HH:MM AM/PM" or "H:MM AM/PM"
    const timeFormat1 = timeStr.match(/(\d+):(\d+)\s*(AM|PM|am|pm)?/i);

    // Format: "HH:MM" (24-hour)
    const timeFormat2 = timeStr.match(/^(\d+):(\d+)$/);

    // Format: "H AM/PM" or "HH AM/PM" (no minutes)
    const timeFormat3 = timeStr.match(/(\d+)\s*(AM|PM|am|pm)/i);

    // Format: "HHMM" (military time without colon)
    const timeFormat4 = timeStr.match(/^(\d{2})(\d{2})$/);

    if (timeFormat1) {
      // Format 1: "HH:MM AM/PM"
      hours = parseInt(timeFormat1[1], 10);
      minutes = parseInt(timeFormat1[2], 10);
      const period = timeFormat1[3] ? timeFormat1[3].toUpperCase() : null;

      console.log(`Parsed format 1: ${hours}:${minutes} ${period || ''}`);

      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }
    }
    else if (timeFormat2) {
      // Format 2: "HH:MM" (24-hour)
      hours = parseInt(timeFormat2[1], 10);
      minutes = parseInt(timeFormat2[2], 10);
      console.log(`Parsed format 2: ${hours}:${minutes}`);
    }
    else if (timeFormat3) {
      // Format 3: "H AM/PM" (no minutes)
      hours = parseInt(timeFormat3[1], 10);
      const period = timeFormat3[2].toUpperCase();
      console.log(`Parsed format 3: ${hours}:00 ${period}`);

      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }
    }
    else if (timeFormat4) {
      // Format 4: "HHMM" (military time without colon)
      hours = parseInt(timeFormat4[1], 10);
      minutes = parseInt(timeFormat4[2], 10);
      console.log(`Parsed format 4: ${hours}:${minutes}`);
    }
    else {
      // If no format matches, try to handle special cases
      if (timeStr.toLowerCase() === "noon") {
        hours = 12;
        minutes = 0;
        console.log(`Parsed special case: noon = 12:00`);
      } else if (timeStr.toLowerCase() === "midnight") {
        hours = 0;
        minutes = 0;
        console.log(`Parsed special case: midnight = 00:00`);      } else {
        // If still no match, return 0
        console.error("Unrecognized time format:", timeStr);
        return 0;
      }
    }

    const totalMinutes = hours * 60 + minutes;
    console.log(`Converted to ${totalMinutes} minutes since midnight`);
    return totalMinutes;
  } catch (e) {
    console.error("Time parsing error:", e, timeStr);
    return 0;
  }
}

// Helper function to format minutes since midnight to a time string
function minutesToTime(minutes: number) {
  let hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  let period = 'AM';

  if (hours >= 12) {
    period = 'PM';
    if (hours > 12) hours -= 12;
  }

  if (hours === 0) hours = 12;

  return `${hours}:${mins.toString().padStart(2, '0')} ${period}`;
}

// Helper function to format time
function formatTimeDisplay(timeStr: string) {
  // Check if the time is already in a good format
  if (!timeStr) return '';

  try {
    // Convert to minutes and back to ensure consistent formatting
    const minutes = timeToMinutes(timeStr);
    return minutesToTime(minutes);
  } catch (e) {
    // If parsing fails, return the original string
    console.error("Time formatting error:", e, timeStr);
    return timeStr;
  }
}

// Simple component for appointment card
function AppointmentCard({ appointment, onClick }: { appointment: any, onClick: (appointment: any) => void }) {
  try {
    // Safety check for appointment object
    if (!appointment || typeof appointment !== 'object') {
      console.error('Invalid appointment object:', appointment);
      return null; // Return null immediately for invalid input
    }

    // Simplified version for debugging
    const appointmentId = appointment.id || 'N/A';
    const patientName = String(appointment.patient_name || appointment.patientName || "No Patient");

    // Handle click event safely
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      if (typeof onClick === 'function') {
        onClick(appointment);
      }
    };

    // Return minimal JSX outside the try block
    return (
      <div
        className="bg-gray-200 dark:bg-gray-700 p-2 rounded mb-2"
        onClick={handleClick}
      >
        Simplified Appointment Card: {patientName} (ID: {appointmentId})
      </div>
    );

  } catch (error) {
    console.error('Error rendering appointment card:', error);
    return null; // Return null if an error occurs during rendering
  }
}

// Helper function to safely sanitize an appointment object
function sanitizeAppointment(appt: any, operatory: string) {
  try {
    // Check if the appointment is a valid object
    if (!appt || typeof appt !== 'object') {
      console.error('Invalid appointment object:', appt);
      return null;
    }

    // Create a new object with only the properties we need
    return {
      id: String(appt.id || `${operatory}-${Math.random().toString(36).substring(2, 9)}`),
      patient_name: String(appt.patient_name || appt.patientName || "No Patient"),
      patientName: String(appt.patient_name || appt.patientName || "No Patient"),
      startTime: String(appt.startTime || "8:00 AM"),
      endTime: String(appt.endTime || "9:00 AM"),
      length: Number(appt.length || 60),
      type: String(appt.type || appt.description || "Unknown"),
      description: String(appt.description || ""),
      notes: String(appt.notes || ""),
      operatory: String(appt.operatory || operatory),
      patient_id: appt.patient_id ? String(appt.patient_id) : (appt.patientId ? String(appt.patientId) : ""),
      provider: (() => {
        try {
          if (appt.provider) {
            // Handle case where provider might be an object
            if (typeof appt.provider === 'object') {
              return appt.provider.name || JSON.stringify(appt.provider);
            } else {
              return String(appt.provider);
            }
          } else if (appt.providerName) {
            // Handle case where providerName might be an object
            if (typeof appt.providerName === 'object') {
              return appt.providerName.name || JSON.stringify(appt.providerName);
            } else {
              return String(appt.providerName);
            }
          }
          return "";
        } catch (error) {
          console.error('Error processing provider in sanitizeAppointment:', error);
          return "";
        }
      })(),
      status: appt.status ? String(appt.status) : "",
      isBlocked: Boolean(appt.isBlocked)
    };
  } catch (error) {
    console.error('Error sanitizing appointment:', error);
    return null;
  }
}

// Time slot component for the grid
function TimeSlot({ time, isHour }: { time: string, isHour: boolean }) {
  return (
    <div className={`h-6 border-b border-gray-200 dark:border-gray-600 flex items-center text-xs text-gray-500 dark:text-gray-400 ${isHour ? 'border-b-2 font-medium' : ''}`}>
      {isHour && (
        <span className="w-16 text-right pr-2">{time}</span>
      )}
    </div>
  );
}

// Grid-based appointment card for time alignment
function GridAppointmentCard({ appointment, onClick, topPosition, height }: { appointment: any, onClick: (appointment: any) => void, topPosition: number, height: number }) {
  try {
    // Safety check for appointment object
    if (!appointment || typeof appointment !== 'object') {
      console.error('Invalid appointment object for GridAppointmentCard:', appointment);
      return null;
    }

    // Get patient name
    let patientName = String(appointment.patient_name || appointment.patientName || "No Patient");

    // Format patient name
    try {
      if (patientName.includes(',')) {
        const parts = patientName.split(',').map(part => part.trim());
        if (parts.length >= 2) {
          const lastName = parts[0];
          const firstParts = parts[1].split(' ');
          patientName = [...firstParts, lastName].join(' ');
        }
      }
      patientName = patientName
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
    } catch (error) {
      console.error('Error formatting patient name:', error);
    }

    // Get provider code for color
    let borderColor = 'border-blue-500';
    let providerCode = '';

    try {
      if (appointment.provider && typeof appointment.provider === 'object') {
        const providerObj = appointment.provider;
        if (providerObj.href) {
          const href = String(providerObj.href);
          const parts = href.split('/');
          providerCode = parts[parts.length - 1].replace(/[\"'\s]+$/, '');
        }
      } else if (appointment.provider) {
        providerCode = String(appointment.provider);
      }
    } catch (error) {
      console.error('Error getting provider code:', error);
    }

    // Assign colors based on the provider code
    if (providerCode) {
      if (providerCode.startsWith('LL') || providerCode.startsWith('DL')) {
        borderColor = 'border-purple-400';
      } else if (providerCode.startsWith('GO') || providerCode.includes('GO')) {
        borderColor = 'border-green-500';
      } else if (providerCode.startsWith('DAZ') || providerCode.includes('DAZ') || providerCode.includes('DZ')) {
        borderColor = 'border-blue-300';
      } else if (providerCode.startsWith('NS') || providerCode.includes('NS')) {
        borderColor = 'border-orange-300';
      } else if (providerCode === 'XOFF') {
        borderColor = 'border-gray-400';
      }
    }

    const appointmentType = String(appointment.type || appointment.description || "Unknown");
    const startTime = formatTimeDisplay(appointment.startTime);
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      if (typeof onClick === 'function') {
        onClick(appointment);
      }
    };
    
    // Original JSX
    return (
      <div
        className={`absolute left-1 right-1 bg-white dark:bg-gray-700 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 border-l-4 ${borderColor} shadow-md text-xs overflow-hidden border border-gray-200 dark:border-gray-600`}
        style={{
          top: `${topPosition}px`,
          height: `${Math.max(height, 48)}px`, // Minimum 48px for better visibility
          zIndex: 10
        }}
        onClick={handleClick}
      >
        <div className="font-semibold text-gray-900 dark:text-white truncate">
          {patientName}
        </div>
        <div className="text-gray-600 dark:text-gray-300 truncate text-xs">
          {startTime}
        </div>
        {height > 30 && ( // Show appointment type for heights > 30px
          <div className="text-gray-500 dark:text-gray-400 truncate text-xs">
            {appointmentType}
          </div>
        )}
        {height <= 30 && ( // For very short appointments, show type in same line as time
          <div className="text-gray-500 dark:text-gray-400 truncate text-xs">
            {appointmentType.length > 10 ? appointmentType.substring(0, 8) + '...' : appointmentType}
          </div>
        )}
      </div>
    );
  } catch (error) { 
    console.error('Error rendering grid appointment card:', error);
    return null;
  }
}

// Grid-based operatory column
function OperatoryColumn({ operatory, appointments, onAppointmentClick, displayStartHour, displayEndHour }: { 
  operatory: string, 
  appointments: any[], 
  onAppointmentClick: (appointment: any) => void,
  displayStartHour: number,
  displayEndHour: number
}) {
  // Filter and sanitize appointments for this operatory
  const operatoryAppointments: any[] = [];
  try {
    if (Array.isArray(appointments)) {
      for (let i = 0; i < appointments.length; i++) {
        const appt = appointments[i];
        if (appt && typeof appt === 'object' && appt.operatory === operatory) {
          const sanitizedAppt = sanitizeAppointment(appt, operatory);
          if (sanitizedAppt) {
            operatoryAppointments.push(sanitizedAppt);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error processing appointments:', error);
  }
  
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = displayStartHour; hour <= displayEndHour; hour++) {
      for (let minute = 0; minute < 60; minute += 10) {
        const isHour = minute === 0;
        // slotIndex is now relative to the dynamic displayStartHour
        slots.push({
          isHour,
          slotIndex: ((hour - displayStartHour) * 6) + (minute / 10)
        });
      }
    }
    return slots;
  }, [displayStartHour, displayEndHour]);

  return (
    <div className="min-w-0 h-full">
      <div className="relative bg-white dark:bg-gray-800 h-full" style={{ height: `${timeSlots.length * 24}px` }}>
        {/* Time slot grid lines - using border-t */}
        {timeSlots.map((slot) => (
          <div
            key={`op-line-${slot.slotIndex}`}
            className={`absolute left-0 right-0 h-6 ${slot.isHour ? 'border-t-2 border-gray-400 dark:border-gray-500' : 'border-t border-gray-300 dark:border-gray-600'}`}
            style={{ top: `${slot.slotIndex * 24}px` }}
          />
        ))}
        {operatoryAppointments.map((appointment) => {
          const startMinutes = timeToMinutes(appointment.startTime);
          // Calculate position relative to the dynamic displayStartHour
          const minutesFromDisplayStart = startMinutes - (displayStartHour * 60);
          const topPosition = (minutesFromDisplayStart / 10) * 24;

          const endMinutes = timeToMinutes(appointment.endTime);
          let duration = endMinutes - startMinutes;
          if (duration <= 0) {
            duration = Number(appointment.length) || 60;
          }
          const height = (duration / 10) * 24;

          if (isAppointment(appointment)) {
            return (
              <GridAppointmentCard
                key={appointment.id}
                appointment={appointment}
                onClick={onAppointmentClick}
                topPosition={topPosition}
                height={height}
              />
            );
          } else {
            return (
              <div
                key={appointment.id}
                className="absolute left-0 right-0 px-1"
                style={{ 
                  top: `${topPosition}px`,
                  height: `${Math.max(height, 24)}px`
                }}
              >
                <ScheduleNote
                  note={appointment}
                  onClick={onAppointmentClick}
                />
              </div>
            );
          }
        })}
      </div>
    </div>
  );
}

// Enhanced Schedule Timeline Component - Multi-column side-by-side operatory layout
function EnhancedScheduleTimeline({ operatories, appointments, onAppointmentClick }: { 
  operatories: string[], 
  appointments: any[], 
  onAppointmentClick: (appointment: any) => void 
}) {
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  useEffect(() => {
    const checkDarkMode = () => {
      const dark = document.documentElement.classList.contains('dark');
      setIsDarkMode(dark);
    };
    checkDarkMode();
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
    return () => observer.disconnect();
  }, []);

  const { displayStartHour, displayEndHour } = useMemo(() => {
    let minHour = 8;
    let maxHour = 17; // Represents the start of the last default hour block (5 PM)

    if (appointments && appointments.length > 0) {
      appointments.forEach(appt => {
        if (isAppointment(appt)) { // Only consider actual appointments for range expansion
          const startMins = timeToMinutes(appt.startTime);
          const endMins = timeToMinutes(appt.endTime);

          if (startMins > 0) { // Check for valid time
            minHour = Math.min(minHour, Math.floor(startMins / 60));
          }
          if (endMins > 0) { // Check for valid time
            // Calculate the hour block an appointment ends in
            // If an appt ends at 17:30, it's in hour 17. floor((1050-1)/60) = 17
            // If an appt ends at 18:00, it's in hour 17 (ends at the boundary). floor((1080-1)/60) = 17
            // If an appt ends at 18:01, it's in hour 18. floor((1081-1)/60) = 18
            const effectiveEndHour = Math.floor((endMins - 1) / 60);
            maxHour = Math.max(maxHour, effectiveEndHour);
          }
        }
      });
    }
    // Ensure there's at least one hour displayed if calculated range is odd.
    // And ensure maxHour is at least the default if no appointments expand it.
    return { 
      displayStartHour: minHour, 
      displayEndHour: Math.max(maxHour, minHour, 17) // Ensure end is at least start and at least 5pm
    };
  }, [appointments]);

  const timeLabels = useMemo(() => {
    const labels = [];
    for (let hour = displayStartHour; hour <= displayEndHour; hour++) {
      for (let minute = 0; minute < 60; minute += 10) {
        const isHour = minute === 0;
        const displayTime = isHour ? formatTimeDisplay(`${hour}:00`) : '';
        labels.push({
          time: displayTime,
          isHour,
          // slotIndex is now relative to the dynamic displayStartHour
          slotIndex: ((hour - displayStartHour) * 6) + (minute / 10) 
        });
      }
    }
    return labels;
  }, [displayStartHour, displayEndHour]);

  return (
    <div className="flex h-full">
      {/* Time Label Column */}
      <div className="relative" style={{ width: '4rem' /* w-16 */ }}>
        {/* Horizontal lines for Time Label Column - using border-t */}
        {timeLabels.map((label) => (
          <div
            key={`time-line-${label.slotIndex}`}
            className={`absolute left-0 right-0 h-6 ${label.isHour ? 'border-t-2 border-gray-400 dark:border-gray-500' : 'border-t border-gray-300 dark:border-gray-600'}`}
            style={{
              top: `${label.slotIndex * 24}px`,
              width: '100%', 
            }}
          />
        ))}
        {/* Time Label Text */}
        {timeLabels.map((label) => {
          const textColor = isDarkMode ? '#e5e7eb' : '#374151'; 
          return (
            label.isHour && ( 
              <div
                key={`time-label-div-${label.slotIndex}`}
                className="h-6 flex items-center justify-end pr-2" 
                style={{
                  position: 'absolute',
                  top: `${label.slotIndex * 24}px`, 
                  width: '100%',
                  fontSize: '0.75rem', 
                }}
              >
                <span
                  style={{
                    color: textColor, 
                    fontWeight: 'bold', // Hour labels are always bold
                    opacity: 1,
                  }}
                >
                  {label.time}
                </span>
              </div>
            )
          );
        })}
      </div>

      {/* Operatory Columns */}
      {operatories.map((operatory) => (
        <div key={operatory} className="flex-1 min-w-0 border-r border-gray-300 dark:border-gray-600 last:border-r-0">
          <OperatoryColumn
            operatory={operatory}
            appointments={appointments}
            onAppointmentClick={onAppointmentClick}
            displayStartHour={displayStartHour}
            displayEndHour={displayEndHour}
          />
        </div>
      ))}
    </div>
  );
}

// Helper function to determine if an item is an appointment (visual block) or a schedule note (text-only)
function isAppointment(item: any): boolean {
  if (!item || typeof item !== 'object') return false;
  
  // An item is considered an appointment if it has:
  // 1. A patient name (not empty or generic)
  // 2. A specific appointment type/description
  // 3. A reasonable time duration
  const hasPatient = item.patient_name && 
                    item.patient_name !== "No Patient" && 
                    item.patient_name.trim() !== "" &&
                    !item.patient_name.toLowerCase().includes("note") &&
                    !item.patient_name.toLowerCase().includes("block") &&
                    !item.patient_name.toLowerCase().includes("break");
  
  const hasAppointmentType = item.type && 
                            item.type.trim() !== "" &&
                            !item.type.toLowerCase().includes("note") &&
                            !item.type.toLowerCase().includes("break") &&
                            !item.type.toLowerCase().includes("block");
  
  const hasReasonableDuration = item.length && Number(item.length) >= 15; // At least 15 minutes
  
  // If it's explicitly marked as blocked or a note, treat as schedule note
  if (item.isBlocked || 
      (item.type && item.type.toLowerCase().includes("note")) ||
      (item.description && item.description.toLowerCase().includes("note")) ||
      (item.patient_name && item.patient_name.toLowerCase().includes("note"))) {
    return false;
  }
  
  return hasPatient && (hasAppointmentType || hasReasonableDuration);
}

// Simple component for schedule notes (text-only display)
function ScheduleNote({ note, onClick }: { note: any, onClick: (note: any) => void }) {
  try {
    if (!note || typeof note !== 'object') {
      console.error('Invalid note object:', note);
      return null;
    }
    let noteText = '';
    if (note.description && note.description.trim() !== '') {
      noteText = String(note.description).trim();
    } else if (note.type && note.type.trim() !== '') {
      noteText = String(note.type).trim();
    } else if (note.patient_name && 
               note.patient_name.trim() !== '' && 
               note.patient_name !== 'No Patient' &&
               !note.patient_name.toLowerCase().includes('no patient')) {
      noteText = String(note.patient_name).trim();
    } else {
      noteText = 'Schedule Note';
    }
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      if (typeof onClick === 'function') {
        onClick(note);
      }
    };
    return (      
      <div
        className="bg-yellow-200 opacity-100 dark:bg-sky-900 p-2 rounded cursor-pointer hover:bg-yellow-300 dark:hover:bg-sky-800 border-l-3 border-yellow-700 dark:border-sky-400 text-xs shadow-sm overflow-y-auto" // Added overflow-y-auto, removed overflow-hidden
        onClick={handleClick}
        style={{ 
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          minHeight: '24px'
        }}
      >
        <div className="text-yellow-900 dark:text-sky-200 w-full leading-tight font-medium whitespace-normal"> {/* Removed truncate, added whitespace-normal */}
          {noteText}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error rendering schedule note:', error);
    return null;
  }
}

// Main schedule page component
export default function SchedulePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [cameFromHome, setCameFromHome] = useState(true);

  // Check where we came from when the component mounts
  useEffect(() => {
    const referrer = document.referrer;
    setCameFromHome(!referrer.includes('operatories'));
  }, []);

  const handleBackClick = () => {
    if (cameFromHome) {
      router.push('/');
    } else {
      router.back();
    }
  };

  // Get date from URL
  const dateParam = searchParams?.get("date") || "";

  // Get date from URL and ensure it's properly formatted
  let formattedDate;
  if (dateParam && dateParam.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // Use the date parameter directly if it's already in YYYY-MM-DD format
    formattedDate = dateParam;
    console.log(`Using date from URL: ${formattedDate}`);
  } else {
    // Use current date as fallback
    const now = new Date();
    formattedDate = format(now, "yyyy-MM-dd");
    console.log(`Using current date: ${formattedDate}`);
  }

  // For testing purposes, uncomment this line to use a specific date
  // formattedDate = "2023-05-22"; // Use a specific date for testing

  // Get operatories from URL
  const operatoryParams = searchParams?.getAll("operatories[]") || [];
  let operatories = operatoryParams.filter(op => op !== 'DAZ1' && op !== 'DAZ2');

  // Sort operatories - first by doctor (DL for Dr. Lowell first), then by number
  operatories.sort((a, b) => {
    // Extract the doctor prefix (e.g., "DL" from "DL01")
    const aPrefix = a.match(/^[A-Z]+/)?.[0] || "";
    const bPrefix = b.match(/^[A-Z]+/)?.[0] || "";

    // Extract the number suffix (e.g., "01" from "DL01")
    const aNum = parseInt(a.replace(/^[A-Z]+/, "") || "0", 10);
    const bNum = parseInt(b.replace(/^[A-Z]+/, "") || "0", 10);

    // Sort by prefix first (DL comes before others)
    if (aPrefix === "DL" && bPrefix !== "DL") return -1;
    if (aPrefix !== "DL" && bPrefix === "DL") return 1;

    // If same prefix, sort by number
    if (aPrefix === bPrefix) return aNum - bNum;

    // Otherwise sort alphabetically by prefix
    return aPrefix.localeCompare(bPrefix);
  });

  console.log(`Sorted operatories: ${operatories.join(', ')}`);

  // Parse the date for display purposes
  const parsedDate = new Date(`${formattedDate}T12:00:00.000Z`);

  // State for selected appointment
  const [selectedAppointment, setSelectedAppointment] = useState(null);

  // Redirect if no date or operatories
  useEffect(() => {
    if (!dateParam || operatories.length === 0) {
      const formattedDate = dateParam || format(new Date(), "yyyy-MM-dd");
      router.push("/operatories?date=" + formattedDate);
    }
  }, [dateParam, operatories, router]);
  // Create a stable fetcher function
  const fetcher = useCallback(async (url: string) => {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch appointments: ${response.status}`);
    }
    return response.json();
  }, []);

  // Build query parameters
  const params = new URLSearchParams();
  params.append("date", formattedDate);
  operatories.forEach(op => params.append("operatories[]", op));
  const queryString = params.toString();

  // Create a stable key for SWR
  const swrKey = formattedDate && operatories.length > 0 ? `/api/appointments?${queryString}` : null;

  // Use SWR for data fetching with caching
  const { data: appointments = [], error, isLoading } = useSWR(
    swrKey,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 300000, // 5 minutes
      refreshInterval: 0, // Don't auto-refresh
      errorRetryCount: 2,
      shouldRetryOnError: false,
    }
  );
  // Handle appointment click
  const handleAppointmentClick = (appointment: any) => {
    setSelectedAppointment(appointment);
  };

  // Close appointment details
  const handleCloseDetails = () => {
    setSelectedAppointment(null);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <PageHeader
        title="Schedule"
        showBackButton
        onBackClick={handleBackClick}
        backButtonLabel={cameFromHome ? 'Back to Home' : 'Back to Operatories'}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Schedule for {format(parsedDate, "MMMM d, yyyy")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Operatories: {operatories.join(", ")}
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error?.message || "Failed to fetch appointments"}</span>
          </div>
        ) : !appointments || appointments.length === 0 ? (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">No appointments found </strong>
            <span className="block sm:inline">for the selected date and operatories.</span>
          </div>        ) : (
          <EnhancedScheduleTimeline
            operatories={operatories}
            appointments={appointments}
            onAppointmentClick={handleAppointmentClick}
          />
        )}
      </main>
    </div>
  );
}
