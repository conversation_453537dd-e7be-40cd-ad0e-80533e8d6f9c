"use client";

import React, { useState, useEffect, useMemo } from "react";
import { Operatory } from "@/lib/api/types";

interface OperatorySelectorProps {
  operatories: Operatory[];
  onSelectionChange: (selectedOperatories: string[]) => void;
}

export function OperatorySelector({ operatories, onSelectionChange }: OperatorySelectorProps) {
  const [selectedOperatories, setSelectedOperatories] = useState<string[]>([]);

  // Memoize operatory providers to prevent unnecessary re-renders
  const operatoryProviders = React.useMemo(() => {
    return Array.from(new Set(operatories.map(op => op.provider)))
      .sort((a, b) => {
        // Put Dr. Lowell and Dr. <PERSON> at the top
        if (a?.includes('Lowell')) return -1;
        if (b?.includes('Lowell')) return 1;
        if (a?.includes('Soto')) return -1;
        if (b?.includes('Soto')) return 1;
        // Then sort alphabetically
        return (a || '').localeCompare(b || '');
      });
  }, [operatories]);

  // Toggle operatory selection
  const toggleOperatory = (operatoryId: string) => {
    setSelectedOperatories((prev) => {
      const isSelected = prev.includes(operatoryId);
      const newSelection = isSelected
        ? prev.filter((id) => id !== operatoryId)
        : [...prev, operatoryId];

      // Notify parent component of selection change
      onSelectionChange(newSelection);
      return newSelection;
    });
  };

  // Select all operatories
  const selectAll = () => {
    const allOperatoryIds = operatories.map((op) => op.id);
    setSelectedOperatories(allOperatoryIds);
    onSelectionChange(allOperatoryIds);
  };

  // Clear all selections
  const clearAll = () => {
    setSelectedOperatories([]);
    onSelectionChange([]);
  };

  return (
    <div className="flex flex-col space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Select Operatories
        </label>
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={selectAll}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded"
          >
            Select All
          </button>
          <button
            type="button"
            onClick={clearAll}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded"
          >
            Clear All
          </button>
        </div>
      </div>

      {/* Group operatories by provider */}
      <div className="space-y-4">
        {/* Use memoized providers list */}
        {operatoryProviders.map(provider => {
          // Skip null or undefined providers
          if (!provider) return null;

          // Get operatories for this provider
          const providerOperatories = operatories
            .filter(op => op.provider === provider)
            .sort((a, b) => (a.sortKey || '').localeCompare(b.sortKey || ''));

          // Skip if no operatories for this provider
          if (providerOperatories.length === 0) return null;

          return (
            <div key={provider} className="space-y-2">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {provider}
              </h3>
              <div className="flex flex-wrap gap-2">
                {providerOperatories.map((operatory) => (
                  <button
                    key={operatory.id}
                    type="button"
                    onClick={() => toggleOperatory(operatory.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      selectedOperatories.includes(operatory.id)
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                    }`}
                  >
                    {operatory.name}
                  </button>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
