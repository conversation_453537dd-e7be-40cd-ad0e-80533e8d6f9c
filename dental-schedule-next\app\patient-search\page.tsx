'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { PageHeader } from '@/components/ui/page-header';
import { Search, User, Calendar, FileText } from 'lucide-react';

interface Patient {
  id: string;
  name: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  middleInitial?: string;
  preferredName?: string;
  dateOfBirth?: string;
  age?: string;
  gender?: string;
  genderInitial?: string;
  phone?: string;
  email?: string;
  lastVisit?: string;
  firstVisit?: string;
  recentVisitCount?: number;
}

interface ClinicalNote {
  id: string;
  date: string;
  provider: string;
  notes: string;
  appointmentType?: string;
  procedures?: string[];
}

interface Visit {
  id: string;
  date: string;
  provider: string;
  appointmentType: string;
  status: string;
  operatory?: string;
}

export default function PatientSearch() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [visits, setVisits] = useState<Visit[]>([]);
  const [selectedVisit, setSelectedVisit] = useState<Visit | null>(null);
  const [clinicalNotes, setClinicalNotes] = useState<ClinicalNote[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingNotes, setIsLoadingNotes] = useState(false);
  const [isLoadingVisits, setIsLoadingVisits] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get URL parameters
  const patientId = searchParams.get('patient');
  const visitId = searchParams.get('visit');
  const query = searchParams.get('q');

  // Load state from URL on mount
  useEffect(() => {
    if (query) {
      setSearchTerm(query);
      // Auto-search if there's a query in URL
      handleSearchWithQuery(query);
    }
  }, [query]);

  useEffect(() => {
    if (patientId && !selectedPatient) {
      // Load patient data if patient ID is in URL
      loadPatientFromUrl(patientId);
    } else if (!patientId && selectedPatient) {
      // Clear patient if no patient ID in URL
      setSelectedPatient(null);
      setVisits([]);
      setSelectedVisit(null);
      setClinicalNotes([]);
    }
  }, [patientId, selectedPatient]);

  useEffect(() => {
    if (visitId && selectedPatient && !selectedVisit) {
      // Load visit data if visit ID is in URL
      loadVisitFromUrl(visitId);
    } else if (!visitId && selectedVisit) {
      // Clear visit if no visit ID in URL
      setSelectedVisit(null);
      setClinicalNotes([]);
    }
  }, [visitId, selectedPatient, selectedVisit]);

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setError('Please enter a search term');
      return;
    }

    // Update URL with search query
    const params = new URLSearchParams();
    params.set('q', searchTerm);
    router.push(`/patient-search?${params.toString()}`);
  };

  const handleSearchWithQuery = async (query: string) => {
    console.log('handleSearchWithQuery called with:', query);
    setIsSearching(true);
    setError(null);

    try {
      const response = await fetch(`/api/patients/search?q=${encodeURIComponent(query)}`);

      if (!response.ok) {
        throw new Error('Failed to search patients');
      }

      const data = await response.json();
      console.log('Search results:', data);
      setSearchResults(data.patients || []);

      if (data.patients?.length === 0) {
        setError('No patients found matching your search');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('Failed to search patients. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handlePatientSelect = (patient: Patient) => {
    console.log('Patient selected:', patient);
    console.log('Patient ID:', patient.id);

    // Update URL to include patient ID
    const params = new URLSearchParams();
    if (searchTerm) params.set('q', searchTerm);
    params.set('patient', patient.id);
    console.log('Navigating to:', `/patient-search?${params.toString()}`);
    router.push(`/patient-search?${params.toString()}`);
  };

  const handleVisitSelect = (visit: Visit) => {
    // Update URL to include visit ID
    const params = new URLSearchParams();
    if (searchTerm) params.set('q', searchTerm);
    if (selectedPatient) params.set('patient', selectedPatient.id);
    params.set('visit', visit.id);
    router.push(`/patient-search?${params.toString()}`);
  };

  const loadPatientFromUrl = async (patientId: string) => {
    // Find patient in search results or load from API
    let patient = searchResults.find(p => p.id === patientId);

    if (!patient) {
      // If patient not in search results, load from API using the search query
      if (query) {
        console.log('Patient not in search results, loading from API with query:', query);
        try {
          const response = await fetch(`/api/patients/search?q=${encodeURIComponent(query)}`);
          if (response.ok) {
            const data = await response.json();
            const foundPatient = data.patients?.find(p => p.id === patientId);
            if (foundPatient) {
              patient = foundPatient;
              // Update search results to include all patients from the search
              setSearchResults(data.patients || []);
            }
          }
        } catch (err) {
          console.error('Error loading patient from API:', err);
        }
      }

      if (!patient) {
        console.warn('Patient not found:', patientId);
        return;
      }
    }

    setSelectedPatient(patient);
    setSelectedVisit(null);
    setClinicalNotes([]);
    setIsLoadingVisits(true);
    setIsLoadingNotes(true);
    setError(null);

    try {
      // Load both visits and clinical notes in parallel
      console.log('Loading data for patient:', patient.id);

      const [visitsResponse, notesResponse] = await Promise.all([
        fetch(`/api/patients/${patient.id}/visits`),
        fetch(`/api/patients/${patient.id}/clinical-notes`)
      ]);

      console.log('Visits response status:', visitsResponse.status);
      console.log('Notes response status:', notesResponse.status);

      if (!visitsResponse.ok) {
        console.error('Visits response error:', await visitsResponse.text());
        throw new Error('Failed to load visits');
      }

      if (!notesResponse.ok) {
        console.error('Notes response error:', await notesResponse.text());
        // Don't throw error for notes, just log it
        console.warn('Clinical notes failed to load, continuing with visits only');
      }

      const visitsData = await visitsResponse.json();
      console.log('Visits data:', visitsData);

      let notesData = { notes: [] };
      if (notesResponse.ok) {
        notesData = await notesResponse.json();
        console.log('Notes data:', notesData);
      }

      setVisits(visitsData.visits || []);
      setClinicalNotes(notesData.notes || []);
      console.log('Set visits:', visitsData.visits?.length || 0);
      console.log('Set notes:', notesData.notes?.length || 0);
    } catch (err) {
      console.error('Patient data loading error:', err);
      setError('Failed to load patient data. Please try again.');
    } finally {
      setIsLoadingVisits(false);
      setIsLoadingNotes(false);
    }
  };

  const loadVisitFromUrl = async (visitId: string) => {
    // Find visit in visits list
    const visit = visits.find(v => v.id === visitId);
    if (!visit) return;

    setSelectedVisit(visit);
    setIsLoadingNotes(true);
    setError(null);

    try {
      const response = await fetch(`/api/patients/${selectedPatient?.id}/visits/${visit.id}/notes`);

      if (!response.ok) {
        throw new Error('Failed to load clinical notes');
      }

      const data = await response.json();
      setClinicalNotes(data.notes || []);
    } catch (err) {
      console.error('Notes loading error:', err);
      setError('Failed to load clinical notes. Please try again.');
    } finally {
      setIsLoadingNotes(false);
    }
  };

  const handleBack = () => {
    if (selectedVisit) {
      // Go back from notes to visits list
      const params = new URLSearchParams();
      if (searchTerm) params.set('q', searchTerm);
      if (selectedPatient) params.set('patient', selectedPatient.id);
      router.push(`/patient-search?${params.toString()}`);
    } else if (selectedPatient) {
      // Go back from patient details to home page patient search tab
      const params = new URLSearchParams();
      params.set('tab', 'patient');
      if (searchTerm) params.set('search', searchTerm);
      router.push(`/?${params.toString()}`);
    } else {
      // Go back to home
      router.push('/');
    }
  };

  const formatPatientName = (patient: Patient) => {
    const parts = [patient.firstName, patient.middleName, patient.lastName].filter(Boolean);
    return parts.join(' ');
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getTimeSinceLastVisit = (lastVisitDate: string) => {
    if (!lastVisitDate) return '';

    try {
      const lastVisit = new Date(lastVisitDate);
      const today = new Date();

      const diffTime = today.getTime() - lastVisit.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 30) {
        return diffDays === 1 ? '1 day' : `${diffDays} days`;
      } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return months === 1 ? '1 month' : `${months} months`;
      } else {
        const years = Math.floor(diffDays / 365);
        const remainingDays = diffDays % 365;
        const months = Math.floor(remainingDays / 30);

        if (months === 0) {
          return years === 1 ? '1 year' : `${years} years`;
        } else {
          const yearText = years === 1 ? '1 year' : `${years} years`;
          const monthText = months === 1 ? '1 month' : `${months} months`;
          return `${yearText} and ${monthText}`;
        }
      }
    } catch {
      return '';
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <PageHeader
        title={selectedPatient ? `${formatPatientName(selectedPatient)} - Clinical Notes` : 'Patient Search'}
        showBackButton={true}
        onBackClick={handleBack}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!selectedPatient ? (
          // Search Interface
          <div className="max-w-2xl mx-auto">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 text-center">
                Search Patients
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
                Search by first name, last name, or partial matches
              </p>

              <div className="flex space-x-2">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    placeholder="Enter patient name..."
                    autoFocus
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <button
                  onClick={handleSearch}
                  disabled={isSearching}
                  className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSearching ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-red-800 dark:text-red-200">{error}</p>
              </div>
            )}

            {searchResults.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Search Results ({searchResults.length})
                </h3>
                <div className="space-y-2">
                  {searchResults.map((patient) => (
                    <button
                      key={patient.id}
                      onClick={() => handlePatientSelect(patient)}
                      className="w-full p-4 text-left bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-700 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <User className="h-5 w-5 text-gray-400" />
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className="font-medium text-gray-900 dark:text-white">
                              {formatPatientName(patient)}
                            </p>
                            {patient.age && patient.genderInitial && (
                              <span className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {patient.age}{patient.genderInitial}
                              </span>
                            )}
                          </div>
                          {patient.dateOfBirth && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              DOB: {formatDate(patient.dateOfBirth)}
                            </p>
                          )}
                          {patient.lastVisit && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Last Visit: {formatDate(patient.lastVisit)}
                              {(() => {
                                const timeSince = getTimeSinceLastVisit(patient.lastVisit);
                                return timeSince ? ` (${timeSince} ago)` : '';
                              })()}
                            </p>
                          )}

                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : !selectedVisit ? (
          // Patient Details Interface (Visits + Clinical Notes)
          <div className="max-w-6xl mx-auto">
            {(isLoadingVisits || isLoadingNotes) ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-300">Loading patient data...</p>
              </div>
            ) : (
              <div>
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    Patient Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-blue-800 dark:text-blue-200">
                        <strong>Name:</strong> {selectedPatient.name}
                      </p>
                      {selectedPatient.age && selectedPatient.genderInitial && (
                        <p className="text-blue-800 dark:text-blue-200">
                          <strong>Age:</strong> {selectedPatient.age}{selectedPatient.genderInitial}
                        </p>
                      )}
                      {selectedPatient.dateOfBirth && (
                        <p className="text-blue-800 dark:text-blue-200">
                          <strong>Date of Birth:</strong> {formatDate(selectedPatient.dateOfBirth)}
                        </p>
                      )}
                    </div>
                    <div>
                      {selectedPatient.lastVisit && (
                        <p className="text-blue-800 dark:text-blue-200">
                          <strong>Last Visit:</strong> {formatDate(selectedPatient.lastVisit)}
                          {(() => {
                            const timeSince = getTimeSinceLastVisit(selectedPatient.lastVisit);
                            return timeSince ? ` (${timeSince} ago)` : '';
                          })()}
                        </p>
                      )}
                      {selectedPatient.phone && (
                        <p className="text-blue-800 dark:text-blue-200">
                          <strong>Phone:</strong> {selectedPatient.phone}
                        </p>
                      )}
                      {selectedPatient.email && (
                        <p className="text-blue-800 dark:text-blue-200">
                          <strong>Email:</strong> {selectedPatient.email}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {(() => {
                  // Combine visits and clinical notes into a timeline
                  const timelineItems = [];

                  // Add visits to timeline
                  visits.forEach(visit => {
                    timelineItems.push({
                      id: `visit-${visit.id}`,
                      date: visit.date,
                      type: 'visit',
                      data: visit
                    });
                  });

                  // Add clinical notes to timeline
                  clinicalNotes.forEach(note => {
                    timelineItems.push({
                      id: `note-${note.id}`,
                      date: note.date,
                      type: 'note',
                      data: note
                    });
                  });

                  // Sort by date (most recent first)
                  timelineItems.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

                  return timelineItems.length > 0 ? (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                        <Calendar className="h-5 w-5 mr-2" />
                        Patient Timeline ({visits.length} visits, {clinicalNotes.length} notes)
                      </h3>
                      <div className="relative">
                        {/* Timeline line */}
                        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>

                        <div className="space-y-6">
                          {timelineItems.map((item, index) => (
                            <div key={item.id} className="relative flex items-start">
                              {/* Timeline dot */}
                              <div className={`relative z-10 flex items-center justify-center w-4 h-4 rounded-full border-2 ${
                                item.type === 'visit'
                                  ? 'bg-blue-500 border-blue-500'
                                  : 'bg-green-500 border-green-500'
                              }`}>
                              </div>

                              {/* Timeline content */}
                              <div className="ml-6 flex-1">
                                {item.type === 'visit' ? (
                                  <button
                                    onClick={() => handleVisitSelect(item.data)}
                                    className="w-full p-4 text-left bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800 transition-colors"
                                  >
                                    <div className="flex items-center justify-between mb-2">
                                      <div className="flex items-center space-x-2">
                                        <Calendar className="h-4 w-4 text-blue-600" />
                                        <span className="font-medium text-blue-900 dark:text-blue-100">
                                          {formatDate(item.data.date)} - Visit
                                        </span>
                                      </div>
                                      <span className="text-sm text-blue-700 dark:text-blue-300">
                                        {item.data.status}
                                      </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <p className="text-sm text-blue-800 dark:text-blue-200">
                                        <strong>Type:</strong> {item.data.appointmentType}
                                      </p>
                                      <p className="text-sm text-blue-700 dark:text-blue-300">
                                        Provider: {item.data.provider}
                                      </p>
                                    </div>
                                    {item.data.operatory && (
                                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                        Operatory: {item.data.operatory}
                                      </p>
                                    )}
                                  </button>
                                ) : (
                                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                    <div className="flex items-center justify-between mb-2">
                                      <div className="flex items-center space-x-2">
                                        <FileText className="h-4 w-4 text-green-600" />
                                        <span className="font-medium text-green-900 dark:text-green-100">
                                          {formatDate(item.data.date)} - Clinical Note
                                        </span>
                                      </div>
                                      <span className="text-sm text-green-700 dark:text-green-300">
                                        Provider: {item.data.provider}
                                      </span>
                                    </div>
                                    {item.data.appointmentType && (
                                      <p className="text-sm text-green-800 dark:text-green-200 mb-2">
                                        <strong>Appointment Type:</strong> {item.data.appointmentType}
                                      </p>
                                    )}
                                    {item.data.procedures && item.data.procedures.length > 0 && (
                                      <p className="text-sm text-green-800 dark:text-green-200 mb-2">
                                        <strong>Procedures:</strong> {item.data.procedures.join(', ')}
                                      </p>
                                    )}
                                    <div className="text-green-900 dark:text-green-100">
                                      <strong>Notes:</strong>
                                      <p className="mt-1 text-sm whitespace-pre-wrap">{item.data.notes}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-300">
                        No visits or clinical notes found for this patient.
                      </p>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        ) : (
          // Visit Details Interface (when a specific visit is selected)
          <div className="max-w-4xl mx-auto">
            {isLoadingNotes ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-300">Loading visit details...</p>
              </div>
            ) : (
              <div>
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    Visit Details - {formatDate(selectedVisit.date)}
                  </h3>
                  <p className="text-blue-800 dark:text-blue-200">
                    <strong>Type:</strong> {selectedVisit.appointmentType}
                  </p>
                  <p className="text-blue-800 dark:text-blue-200">
                    <strong>Provider:</strong> {selectedVisit.provider}
                  </p>
                  <p className="text-blue-800 dark:text-blue-200">
                    <strong>Status:</strong> {selectedVisit.status}
                  </p>
                  {selectedVisit.operatory && (
                    <p className="text-blue-800 dark:text-blue-200">
                      <strong>Operatory:</strong> {selectedVisit.operatory}
                    </p>
                  )}
                </div>

                {clinicalNotes.length > 0 ? (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      Clinical Notes for this Visit
                    </h3>
                    <div className="space-y-4">
                      {clinicalNotes.map((note) => (
                        <div
                          key={note.id}
                          className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="font-medium text-gray-900 dark:text-white">
                                {formatDate(note.date)}
                              </span>
                            </div>
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              Provider: {note.provider}
                            </span>
                          </div>
                          {note.appointmentType && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              <strong>Appointment Type:</strong> {note.appointmentType}
                            </p>
                          )}
                          {note.procedures && note.procedures.length > 0 && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              <strong>Procedures:</strong> {note.procedures.join(', ')}
                            </p>
                          )}
                          <div className="text-gray-800 dark:text-gray-200">
                            <strong>Notes:</strong>
                            <p className="mt-1 whitespace-pre-wrap">{note.notes}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-300">
                      No clinical notes found for this visit.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
}
