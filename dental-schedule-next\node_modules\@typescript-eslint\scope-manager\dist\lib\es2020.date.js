"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2020_date = void 0;
const base_config_1 = require("./base-config");
const es2020_intl_1 = require("./es2020.intl");
exports.es2020_date = {
    libs: [es2020_intl_1.es2020_intl],
    variables: [['Date', base_config_1.TYPE]],
};
