# Code Review Plan

## Diagram

```mermaid
flowchart TD
    subgraph Orchestrator
      C[conductor.py]
      AT[audio_transfer.py]
      DB[dental_app.db]
    end

    subgraph Next.js App
      NX[next.config.js]
      API[/src/app/api…/route.ts/]
      UI[/src/app/page.tsx,/components/…/]
      SIK(┌─ src/lib/sikka-client.ts ─┐)
    end

    C -->|1. Create DB schema| DB
    C -->|2. Run audio transfer| AT
    C -->|3. Check/install deps| NX
    C -->|4. Start dev server| NX

    AT -.-> DB
    API -->|fetch| SIK
    UI -->|renders| API
    UI -->|reads/writes| DB
```

## Steps

1. Project Overview  
   - Confirm high-level goals (audio ingest, transcription, AI summaries, UI)  
   - Map out folders: root scripts, `archive/`, `dental-schedule-next/`

2. Orchestrator (`conductor.py`)  
   - Verify startup sequence (DB schema → audio transfer → npm install → Next.js)  
   - Inspect error-handling, configurability (CLI flags), and logging  

3. Database Schema & Data Flow  
   - Examine tables: `audio_recordings`, `appointments_cache`, `audio_appointment_links`  
   - Ensure indexing, foreign keys, and schema match requirements  

4. Audio Transfer & Archive Scripts  
   - Review `archive/audio_transfer.py` for file discovery, integrity checks, receipts  
   - Check how transfers integrate with DB and transcript pipeline  

5. Next.js Application  
   - Validate API routes under `/src/app/api` and Sikka client integration  
   - Review core pages (`page.tsx`), components (calendar, date-picker, voice interface)  
   - Confirm theme-provider, global styles, and hydration patterns  

6. Integration Points  
   - Orchestrator → Next.js via `npm run dev`  
   - UI → DB caching via API  
   - Audio recordings → transcription/A.I. pipelines (future work)  

7. Gap Analysis & Recommendations  
   - Identify missing tests, error handling, environment-variable usage  
   - Suggest improvements (CI setup, type safety, modularization)