#!/usr/bin/env python3
"""
Dental Schedule Application Conductor
=====================================

This script orchestrates the complete dental schedule application startup:
1. Runs audio transfer from USB drives
2. Checks and installs Next.js dependencies
3. Starts the Next.js development server
4. Creates database schema if needed
5. Provides a single entry point for the entire application

Usage:
    python conductor.py [--skip-audio] [--port 3001] [--daily-receipts]
"""

import subprocess
import sys
import os
import time
import json
import sqlite3
from pathlib import Path
from datetime import datetime
import argparse

# Configuration
DEFAULT_PORT = 3001
WORKSPACE_ROOT = Path(__file__).parent
DENTAL_NEXT_DIR = WORKSPACE_ROOT / "dental-schedule-next"
ARCHIVE_DIR = WORKSPACE_ROOT / "archive"
DB_PATH = WORKSPACE_ROOT / "dental_app.db"

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header(message):
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{message.center(60)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}\n")

def print_success(message):
    print(f"{Colors.OKGREEN}✓ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.OKBLUE}ℹ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def create_database_schema():
    """Create the database schema for audio recordings and appointments."""
    print_info("Creating database schema...")
    
    schema_sql = """
    -- Audio Recordings Table
    CREATE TABLE IF NOT EXISTS audio_recordings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id TEXT NOT NULL,
        recording_date TEXT NOT NULL,
        filename TEXT NOT NULL,
        src_path TEXT NOT NULL,
        dst_path TEXT NOT NULL,
        size_bytes INTEGER NOT NULL,
        sha256_hash TEXT NOT NULL,
        transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        verified_flag BOOLEAN DEFAULT TRUE,
        receipt_path TEXT,
        -- Transcription fields
        transcription_text TEXT,
        transcription_confidence REAL,
        transcription_timestamp TIMESTAMP,
        -- AI Summary fields
        ai_summary TEXT,
        ai_key_points TEXT,
        ai_timestamp TIMESTAMP,
        -- Appointment linkage
        appointment_id TEXT,
        patient_id TEXT,
        linked_at TIMESTAMP,
        -- Status tracking
        status TEXT DEFAULT 'imported', -- imported, transcribed, summarized, linked, archived
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Appointments Cache Table (from Sikka API)
    CREATE TABLE IF NOT EXISTS appointments_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        appointment_id TEXT UNIQUE NOT NULL,
        patient_id TEXT,
        patient_name TEXT,
        appointment_date TEXT NOT NULL,
        appointment_time TEXT,
        operatory TEXT,
        provider_code TEXT,
        procedure_codes TEXT, -- JSON array
        status TEXT,
        notes TEXT,
        fetched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Audio-Appointment Links Table
    CREATE TABLE IF NOT EXISTS audio_appointment_links (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        audio_recording_id INTEGER NOT NULL,
        appointment_id TEXT NOT NULL,
        confidence_score REAL DEFAULT 1.0, -- How confident we are in this link
        link_method TEXT, -- 'manual', 'time_match', 'ai_analysis'
        created_by TEXT DEFAULT 'system',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (audio_recording_id) REFERENCES audio_recordings(id),
        FOREIGN KEY (appointment_id) REFERENCES appointments_cache(appointment_id)
    );

    -- Indexes for performance
    CREATE INDEX IF NOT EXISTS idx_audio_recordings_date ON audio_recordings(recording_date);
    CREATE INDEX IF NOT EXISTS idx_audio_recordings_device ON audio_recordings(device_id);
    CREATE INDEX IF NOT EXISTS idx_audio_recordings_status ON audio_recordings(status);
    CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments_cache(appointment_date);
    CREATE INDEX IF NOT EXISTS idx_appointments_patient ON appointments_cache(patient_id);
    """
    
    try:
        with sqlite3.connect(DB_PATH) as conn:
            conn.executescript(schema_sql)
        print_success(f"Database schema created at {DB_PATH}")
        return True
    except Exception as e:
        print_error(f"Failed to create database schema: {e}")
        return False

def run_audio_transfer(daily_receipts=False):
    """Run the audio transfer script."""
    print_header("Running Audio Transfer")
    
    audio_script = ARCHIVE_DIR / "audio_transfer.py"
    if not audio_script.exists():
        print_warning("Audio transfer script not found, skipping...")
        return True
    
    cmd = [sys.executable, str(audio_script)]
    if daily_receipts:
        cmd.append("--daily")
    
    try:
        print_info(f"Executing: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(ARCHIVE_DIR))
        
        if result.returncode == 0:
            print_success("Audio transfer completed successfully")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print_error(f"Audio transfer failed with code {result.returncode}")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"Failed to run audio transfer: {e}")
        return False

def find_node_command():
    """Find the Node.js command (npm) in various locations."""
    possible_locations = [
        "npm",  # Try PATH first
        r"C:\Program Files\nodejs\npm.cmd",
        r"C:\Program Files (x86)\nodejs\npm.cmd",
        r"C:\Users\<USER>\AppData\Roaming\npm\npm.cmd",
        r"C:\tools\nodejs\npm.cmd",
    ]
    
    for cmd in possible_locations:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print_info(f"Found npm at: {cmd}")
                return cmd
        except:
            continue
    
    return None

def check_node_dependencies():
    """Check if Node.js dependencies are installed."""
    print_header("Checking Node.js Dependencies")
    
    if not DENTAL_NEXT_DIR.exists():
        print_error(f"Next.js directory not found: {DENTAL_NEXT_DIR}")
        return False
    
    node_modules = DENTAL_NEXT_DIR / "node_modules"
    package_json = DENTAL_NEXT_DIR / "package.json"
    
    if not package_json.exists():
        print_error("package.json not found")
        return False
    
    # Find npm command
    npm_cmd = find_node_command()
    if not npm_cmd:
        print_error("Node.js/npm not found. Please install Node.js from https://nodejs.org/")
        print_warning("Continuing without dependency check...")
        return True  # Don't fail the whole process
    
    if node_modules.exists():
        print_success("Dependencies already installed")
        return True
    
    print_info("Installing dependencies...")
    try:
        result = subprocess.run(
            [npm_cmd, "install"], 
            cwd=str(DENTAL_NEXT_DIR),
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print_success("Dependencies installed successfully")
            return True
        else:
            print_error(f"npm install failed: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"Failed to install dependencies: {e}")
        return False

def start_next_server(port=DEFAULT_PORT):
    """Start the Next.js development server."""
    print_header(f"Starting Next.js Development Server on Port {port}")
    
    # Find npm command
    npm_cmd = find_node_command()
    if not npm_cmd:
        print_error("Node.js/npm not found. Please install Node.js from https://nodejs.org/")
        print_info("You can download Node.js from: https://nodejs.org/en/download/")
        return False
    
    try:        # Set the port via environment variable
        env = os.environ.copy()
        env["PORT"] = str(port)
        
        print_info(f"Starting server at http://localhost:{port}")
        print_success(f"🌐 Web app will be available at: http://localhost:{port}")
        print_success("🚀 CONDUCTOR READY - Server is starting up...")
        print_info("Press Ctrl+C to stop the server")
        print_warning("Switching to server output...")
        
        # Start the server in foreground mode
        result = subprocess.run(
            [npm_cmd, "run", "dev"],
            cwd=str(DENTAL_NEXT_DIR),
            env=env
        )
        
        print_success("🏁 CONDUCTOR FINISHED - Server has stopped")
        return result.returncode == 0
    except KeyboardInterrupt:
        print_info("\nServer stopped by user")
        print_success("🏁 CONDUCTOR FINISHED - Server stopped by user")
        return True
    except Exception as e:
        print_error(f"Failed to start Next.js server: {e}")
        return False

def create_startup_log():
    """Create a startup log entry."""
    log_file = WORKSPACE_ROOT / "startup.log"
    timestamp = datetime.now().isoformat()
    
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"{timestamp} - Conductor started\n")

def display_app_info():
    """Display application information."""
    print_header("Dental Schedule Application")
    print_info("Features:")
    print("  • Audio transfer from USB drives to network share")
    print("  • SHA-256 verification and JSON receipts")
    print("  • Automatic trash management with 30-day retention")
    print("  • Modern Next.js web interface")
    print("  • Sikka API integration for appointment data")
    print("  • Audio transcription and AI summarization")
    print("  • Appointment linking and clinical notes")
    print()

def main():
    parser = argparse.ArgumentParser(description="Dental Schedule Application Conductor")
    parser.add_argument("--skip-audio", action="store_true", help="Skip audio transfer step")
    parser.add_argument("--port", type=int, default=DEFAULT_PORT, help=f"Port for Next.js server (default: {DEFAULT_PORT})")
    parser.add_argument("--daily-receipts", action="store_true", help="Use daily receipt grouping for audio transfer")
    parser.add_argument("--setup-only", action="store_true", help="Only setup database and dependencies, don't start server")
    
    args = parser.parse_args()
    
    display_app_info()
    create_startup_log()
    
    # Step 1: Create database schema
    if not create_database_schema():
        print_error("Database setup failed, continuing anyway...")
    
    # Step 2: Run audio transfer (unless skipped)
    if not args.skip_audio:
        if not run_audio_transfer(args.daily_receipts):
            print_warning("Audio transfer failed, but continuing with web server...")
    else:
        print_info("Skipping audio transfer as requested")
    
    # Step 3: Check/install Node.js dependencies
    if not check_node_dependencies():
        print_error("Failed to setup Node.js dependencies")
        return 1
    
    # Step 4: Start the Next.js server (unless setup-only)
    if args.setup_only:
        print_success("Setup completed. Run without --setup-only to start the server.")
        return 0
    
    if not start_next_server(args.port):
        print_error("Failed to start Next.js server")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_info("\nApplication stopped by user")
        sys.exit(0)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)
