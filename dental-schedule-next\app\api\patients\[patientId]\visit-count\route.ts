import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ patientId: string }> }
) {
  try {
    const { patientId } = await params;
    const { searchParams } = new URL(request.url);
    const months = parseInt(searchParams.get('months') || '6');

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Getting visit count for patient: ${patientId} (last ${months} months)`);

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();
    const visitCount = await sikkaClient.getPatientRecentVisitCount(patientId, months);

    console.log(`API: Found ${visitCount} visits for patient ${patientId} in last ${months} months`);

    return NextResponse.json({
      patientId,
      visitCount,
      months,
      period: `${months} months`
    });

  } catch (error) {
    console.error('Visit count API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch visit count' },
      { status: 500 }
    );
  }
}
