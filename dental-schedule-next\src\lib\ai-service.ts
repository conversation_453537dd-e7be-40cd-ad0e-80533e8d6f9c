import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface SikkaAppointment {
  appointment_id: string;
  patient_id: string;
  appointment_date: string;
  appointment_time: string;
  operatory: string;
  provider_code: string;
  procedure_codes?: string[];
  status: string;
  notes?: string;
}

interface AnonymizedData {
  type: 'appointments' | 'procedures' | 'summary';
  data: any;
  metadata: {
    total_count: number;
    date_range?: string;
    anonymization_applied: boolean;
  };
}

export class AIService {
  // Remove all PHI from data before sending to OpenAI
  static anonymizeData(data: any[]): AnonymizedData {
    const anonymized = data.map((item, index) => {
      const cleaned: any = {};
      
      // Keep only non-PHI fields
      const allowedFields = [
        'appointment_date',
        'appointment_time', 
        'operatory',
        'provider_code',
        'procedure_codes',
        'status',
        'appointment_type',
        'duration'
      ];

      allowedFields.forEach(field => {
        if (item[field] !== undefined) {
          cleaned[field] = item[field];
        }
      });

      // Replace patient_id with anonymized reference
      if (item.patient_id) {
        cleaned.patient_ref = `Patient_${index + 1}`;
      }

      // Clean any notes - remove potential PHI
      if (item.notes) {
        cleaned.notes = this.sanitizeNotes(item.notes);
      }

      return cleaned;
    });

    return {
      type: 'appointments',
      data: anonymized,
      metadata: {
        total_count: data.length,
        anonymization_applied: true
      }
    };
  }

  // Remove potential PHI from text notes
  static sanitizeNotes(notes: string): string {
    if (!notes) return '';
    
    // Remove common PHI patterns
    let sanitized = notes
      // Remove phone numbers
      .replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE]')
      // Remove email addresses
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
      // Remove addresses (basic pattern)
      .replace(/\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\b/gi, '[ADDRESS]')
      // Remove SSN patterns
      .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]')
      // Remove dates of birth patterns
      .replace(/\b(?:DOB|Date of Birth|Born):?\s*\d{1,2}\/\d{1,2}\/\d{2,4}\b/gi, '[DOB]');

    return sanitized;
  }

  // Generate aggregated statistics for AI analysis
  static generateStats(appointments: SikkaAppointment[]): any {
    const stats = {
      total_appointments: appointments.length,
      date_range: {
        start: appointments.length > 0 ? Math.min(...appointments.map(a => new Date(a.appointment_date).getTime())) : null,
        end: appointments.length > 0 ? Math.max(...appointments.map(a => new Date(a.appointment_date).getTime())) : null
      },
      by_operatory: {} as Record<string, number>,
      by_provider: {} as Record<string, number>,
      by_month: {} as Record<string, number>,
      by_procedure: {} as Record<string, number>,
      by_status: {} as Record<string, number>
    };

    appointments.forEach(apt => {
      // Count by operatory
      stats.by_operatory[apt.operatory] = (stats.by_operatory[apt.operatory] || 0) + 1;
      
      // Count by provider
      stats.by_provider[apt.provider_code] = (stats.by_provider[apt.provider_code] || 0) + 1;
      
      // Count by month
      const month = new Date(apt.appointment_date).toISOString().substring(0, 7);
      stats.by_month[month] = (stats.by_month[month] || 0) + 1;
      
      // Count by status
      stats.by_status[apt.status] = (stats.by_status[apt.status] || 0) + 1;
      
      // Count procedures
      if (apt.procedure_codes) {
        apt.procedure_codes.forEach(code => {
          stats.by_procedure[code] = (stats.by_procedure[code] || 0) + 1;
        });
      }
    });

    return stats;
  }

  // Interpret natural language query and determine what data to fetch
  static async interpretQuery(query: string): Promise<{
    intent: string;
    parameters: any;
    sikka_endpoints: string[];
  }> {
    const systemPrompt = `You are a dental practice data analyst with reasoning capabilities. Analyze the user's query and determine the best approach to gather relevant data.

REASONING PROCESS:
1. Understand the core intent behind the question
2. Consider what data would be most relevant
3. Think about potential follow-up questions that might arise
4. Determine the optimal data collection strategy

Available endpoints:
- /appointments (v2) - appointment data, scheduling, operatory usage
- /patients (v2) - patient demographics (use sparingly, contains PHI)
- /procedures (v4) - procedure and treatment data, clinical notes
- /operatories - operatory information and utilization

Common dental procedures and CDT codes:
- D0120: Periodic oral evaluation (checkups)
- D1110: Prophylaxis (cleanings)
- D2140-D2394: Restorative procedures (fillings, crowns)
- D4341-D4342: Periodontal scaling (deep cleanings)
- D7140: Extraction
- D6240-D6794: Crowns and bridges
- D9110: Palliative treatment
- D0210: Intraoral radiographs

ANALYSIS APPROACH:
- For trend questions: Consider appropriate time ranges
- For comparison questions: Think about what metrics to compare
- For operational questions: Focus on efficiency and utilization data
- For clinical questions: Consider procedure codes and treatment patterns

Respond in JSON format only:
{
  "intent": "detailed description of what user wants to understand",
  "parameters": {
    "date_range": "specific range or relative (e.g., 'last_month', 'last_quarter')",
    "procedure_codes": ["specific CDT codes if mentioned"],
    "analysis_type": "trend|comparison|summary|operational",
    "filters": {}
  },
  "sikka_endpoints": ["prioritized list of endpoints to call"]
}`;

    try {
      const response = await openai.chat.completions.create({
        model: "o1-mini",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: query }
        ],
        max_completion_tokens: 500
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return JSON.parse(content);
    } catch (error) {
      console.error('Error interpreting query:', error);
      // Fallback interpretation
      return {
        intent: "General practice data query",
        parameters: {},
        sikka_endpoints: ["/appointments"]
      };
    }
  }

  // Generate natural language response from anonymized data
  static async generateResponse(
    query: string,
    anonymizedData: AnonymizedData,
    stats: any
  ): Promise<string> {
    const systemPrompt = `You are an AI Office Manager and Sikka API expert for a dental practice with advanced reasoning capabilities. You have FULL ACCESS to live practice data through the Sikka API and can analyze real-time information.

YOUR CAPABILITIES:
- You have direct access to Sikka API data including appointments, procedures, operatories, and patient information
- You can pull and analyze real practice data to answer any question
- You are an expert in dental practice operations, CDT codes, and Sikka API structure
- You can reason through complex queries and provide data-driven insights

CRITICAL SECURITY RULES:
- The data has been anonymized - no patient names or PHI are included
- Only refer to patients as "Patient_1", "Patient_2", etc. if needed
- Focus on aggregated statistics and trends
- Never make up specific patient information
- Be helpful but maintain professional medical practice standards

DATA ACCESS:
- You have access to REAL appointment data, not sample data
- You can analyze actual procedure codes, operatory usage, provider productivity
- You can identify real trends, patterns, and operational insights
- When you see "0 appointments" it means there actually were no appointments of that type in the specified period

REASONING APPROACH:
- Analyze the actual data provided to give real insights
- If the query is unclear, ask specific questions to provide better analysis
- Consider multiple angles of analysis for complex questions
- If data seems limited, suggest expanding the date range or looking at related metrics

RESPONSE TYPES:
1. Data-Driven Answer: When you have sufficient real data to provide concrete insights
2. Clarifying Questions: When you need more specifics to provide the best analysis
3. Expanded Analysis: When you can provide insights plus suggest related metrics to explore

Provide insights about:
- Actual appointment volumes and trends from real data
- Real procedure patterns and frequencies
- Actual operatory utilization rates
- Real provider productivity metrics
- Genuine scheduling patterns and efficiency
- Financial implications based on actual data
- Operational efficiency from real practice metrics

Remember: You are analyzing REAL practice data, not hypothetical scenarios. Provide concrete, actionable insights based on the actual data.`;

    try {
      const response = await openai.chat.completions.create({
        model: "o4-mini",
        messages: [
          { role: "system", content: systemPrompt },
          {
            role: "user",
            content: `Query: "${query}"\n\nAnonymized Data: ${JSON.stringify(anonymizedData, null, 2)}\n\nStatistics: ${JSON.stringify(stats, null, 2)}`
          }
        ],
        max_completion_tokens: 1000
      });

      return response.choices[0]?.message?.content || 'I apologize, but I was unable to generate a response for your query.';
    } catch (error) {
      console.error('Error generating response:', error);
      return 'I encountered an error while processing your request. Please try rephrasing your question.';
    }
  }
}
