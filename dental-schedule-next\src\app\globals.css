@import "tailwindcss";

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Simple dark mode with CSS variables */
:root {
  --bg-color: #ffffff;
  --text-color: #000000;
  --header-bg: #f3f4f6;
  --card-bg: #ffffff;
  --border-color: #e5e7eb;
  --button-bg: #e5e7eb;
  --button-text: #374151;
}

.dark {
  --bg-color: #111827;
  --text-color: #f9fafb;
  --header-bg: #1f2937;
  --card-bg: #1f2937;
  --border-color: #374151;
  --button-bg: #374151;
  --button-text: #e5e7eb;
}

body {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Apply dark mode to common elements */
.min-h-screen {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

header {
  background-color: var(--header-bg) !important;
}

.bg-gray-100, .bg-gray-200 {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
}

.text-gray-900 {
  color: var(--text-color) !important;
}

.text-gray-600 {
  color: var(--text-color) !important;
  opacity: 0.8;
}

/* Calendar specific dark mode fixes */
.bg-white {
  background-color: var(--card-bg) !important;
}

.text-gray-700 {
  color: var(--text-color) !important;
  opacity: 0.9;
}

.text-gray-500 {
  color: var(--text-color) !important;
  opacity: 0.7;
}

.bg-gray-800 {
  background-color: var(--card-bg) !important;
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* Schedule time labels - override global styles with stronger specificity */
.schedule-time-label,
span.schedule-time-label,
.schedule-time-label.text-xs,
.text-xs.font-medium.schedule-time-label {
  color: #374151 !important; /* Gray-700 for light mode */
  opacity: 1 !important; /* Override any opacity rules */
}

.dark .schedule-time-label,
.dark span.schedule-time-label,
.dark .schedule-time-label.text-xs,
.dark .text-xs.font-medium.schedule-time-label {
  color: #e5e7eb !important; /* Gray-200 for dark mode */
  opacity: 1 !important; /* Override any opacity rules */
}

/* Additional override for any gray text classes that might interfere */
.schedule-time-label.text-gray-500,
.schedule-time-label.text-gray-400,
.schedule-time-label.text-gray-600 {
  color: #374151 !important; /* Force light mode color */
}

.dark .schedule-time-label.text-gray-500,
.dark .schedule-time-label.text-gray-400,
.dark .schedule-time-label.text-gray-600 {
  color: #e5e7eb !important; /* Force dark mode color */
}

/* Enhanced time label override with maximum specificity */
.schedule-time-label-override {
  color: #374151 !important; /* Light mode color */
  opacity: 1 !important;
  font-weight: bold !important;
  font-size: 0.75rem !important;
}

.dark .schedule-time-label-override {
  color: #e5e7eb !important; /* Dark mode color */
  opacity: 1 !important;
  font-weight: bold !important;
  font-size: 0.75rem !important;
}
