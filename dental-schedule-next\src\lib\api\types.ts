/**
 * Types for Sikka API data
 */

export interface Credentials {
  app_id: string;
  app_key: string;
  office_id: string;
  secret_key: string;
}

export interface AuthResponse {
  request_key: string;
  expires_in?: number;
}

export interface Operatory {
  id: string;
  name: string;
  provider?: string;
  sortKey?: string;
  resource_id?: string;
}

export interface Appointment {
  id: string;
  appointment_sr_no?: string;
  patient_id?: string;
  patient_name: string;
  provider?: string;
  operatory: string;
  date: string;
  startTime: string;
  endTime: string;
  length: number;
  type: string;
  description: string;
  isBlocked: boolean;
  status?: string;
}

export interface MedicalNote {
  id: string;
  patient_id?: string;
  patient_name?: string;
  date: string;
  provider?: string;
  note: string;
  note_type?: string;
  created_date?: string;
}

export interface ApiError {
  status: number;
  message: string;
}
