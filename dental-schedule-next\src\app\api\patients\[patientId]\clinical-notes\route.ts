import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ patientId: string }> }
) {
  try {
    const { patientId } = await params;

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Fetching clinical notes for patient: ${patientId}`);

    // For now, return empty notes to focus on getting visits working
    // TODO: Implement actual clinical notes fetching
    const notes = [];

    console.log(`API: Found ${notes.length} clinical notes for patient ${patientId}`);

    return NextResponse.json({
      notes,
      total: notes.length,
      patientId
    });

  } catch (error) {
    console.error('Clinical notes API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch clinical notes' },
      { status: 500 }
    );
  }
}
