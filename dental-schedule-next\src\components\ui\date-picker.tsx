"use client";

import { useState } from "react";
import { format } from "date-fns";

interface DatePickerProps {
  initialDate?: Date;
  onDateChange: (date: Date) => void;
}

export function DatePicker({ initialDate = new Date(), onDateChange }: DatePickerProps) {
  const [date, setDate] = useState(initialDate);

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(e.target.value);
    setDate(newDate);
    onDateChange(newDate);
  };

  return (
    <div className="flex flex-col space-y-1">
      <label htmlFor="date-picker" className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Select Date
      </label>
      <input
        id="date-picker"
        type="date"
        value={format(date, "yyyy-MM-dd")}
        onChange={handleDateChange}
        className="px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
      />
    </div>
  );
}
