import { NextRequest, NextResponse } from 'next/server';
import { readdir, stat } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function GET() {
  try {
    const recordings = [];
    
    // Check network share first
    const networkPath = '\\\\192.168.0.2\\share\\recordings';
    try {
      const networkRecordings = await getRecordingsFromPath(networkPath, true);
      recordings.push(...networkRecordings);
    } catch (error) {
      console.log('Network share not available');
    }
    
    // Check local backup
    const localBackupPath = path.join(process.cwd(), 'voice-recordings');
    if (existsSync(localBackupPath)) {
      try {
        const localRecordings = await getRecordingsFromPath(localBackupPath, false);
        recordings.push(...localRecordings);
      } catch (error) {
        console.error('Failed to read local recordings:', error);
      }
    }
    
    // Remove duplicates and sort by creation date
    const uniqueRecordings = recordings.reduce((acc, current) => {
      const existing = acc.find(item => item.name === current.name);
      if (!existing) {
        acc.push(current);
      }
      return acc;
    }, [] as any[]);
    
    uniqueRecordings.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return NextResponse.json({ recordings: uniqueRecordings });
    
  } catch (error) {
    console.error('Failed to load recordings:', error);
    return NextResponse.json({ 
      error: 'Failed to load recordings',
      recordings: []
    });
  }
}

async function getRecordingsFromPath(dirPath: string, isNetwork: boolean) {
  const recordings = [];
  
  try {
    const files = await readdir(dirPath);
    
    for (const file of files) {
      if (file.match(/\.(webm|mp3|wav|m4a|ogg)$/i)) {
        try {
          const filePath = path.join(dirPath, file);
          const stats = await stat(filePath);
          
          recordings.push({
            id: `${isNetwork ? 'net' : 'local'}-${file}`,
            name: file,
            duration: 0, // Would need audio analysis to get actual duration
            size: stats.size,
            createdAt: stats.birthtime || stats.mtime,
            isUploaded: isNetwork,
            localPath: isNetwork ? null : filePath,
            networkPath: isNetwork ? filePath : null
          });
        } catch (statError) {
          console.error(`Failed to get stats for ${file}:`, statError);
        }
      }
    }
  } catch (readdirError) {
    console.error(`Failed to read directory ${dirPath}:`, readdirError);
    throw readdirError;
  }
  
  return recordings;
}
