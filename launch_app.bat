@echo off
REM Launch Audio Transfer GUI in new window
echo Launching Audio Transfer GUI...
start "" cmd /k "python \"%~dp0archive\audio_transfer_gui.py\""

REM Launch Next.js development server in new window
echo Launching Next.js development server...
start "" cmd /k "cd \"%~dp0dental-schedule-next\" && npm run dev"

REM Wait a moment and open the browser to the Next.js app
timeout /t 3 /nobreak > nul
echo Opening browser to http://localhost:3001...
start http://localhost:3001

echo All processes have been started.