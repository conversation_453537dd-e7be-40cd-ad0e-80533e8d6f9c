"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2020_bigint = void 0;
const base_config_1 = require("./base-config");
const es2020_intl_1 = require("./es2020.intl");
exports.es2020_bigint = {
    libs: [es2020_intl_1.es2020_intl],
    variables: [
        ['BigIntToLocaleStringOptions', base_config_1.TYPE],
        ['BigInt', base_config_1.TYPE_VALUE],
        ['BigIntConstructor', base_config_1.TYPE],
        ['BigInt64Array', base_config_1.TYPE_VALUE],
        ['BigInt64ArrayConstructor', base_config_1.TYPE],
        ['BigUint64Array', base_config_1.TYPE_VALUE],
        ['BigUint64ArrayConstructor', base_config_1.TYPE],
        ['DataView', base_config_1.TYPE],
        ['Intl', base_config_1.TYPE_VALUE],
    ],
};
