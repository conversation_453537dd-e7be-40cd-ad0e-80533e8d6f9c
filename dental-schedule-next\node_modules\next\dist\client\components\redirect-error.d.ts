import { RedirectStatusCode } from './redirect-status-code';
export declare const REDIRECT_ERROR_CODE = "NEXT_REDIRECT";
export declare enum RedirectType {
    push = "push",
    replace = "replace"
}
export type RedirectError = Error & {
    digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`;
};
/**
 * Checks an error to determine if it's an error generated by the
 * `redirect(url)` helper.
 *
 * @param error the error that may reference a redirect error
 * @returns true if the error is a redirect error
 */
export declare function isRedirectError(error: unknown): error is RedirectError;
